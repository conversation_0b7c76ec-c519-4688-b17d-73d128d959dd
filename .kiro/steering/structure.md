# Project Structure

## Directory Organization

```
src/
├── components/          # Reusable React components
│   ├── Button/         # Button components and variants
│   ├── Form/           # Form-related components
│   ├── Head/           # Header and navigation components
│   ├── List/           # List display components
│   ├── Modal/          # Modal dialogs and popups
│   ├── Table/          # Table components
│   ├── Tabs/           # Tab navigation components
│   └── Transfer/       # Data transfer components
├── hooks/              # Custom React hooks
├── layouts/            # Page layout components
├── pages/              # Page-level components organized by feature
│   ├── config-settings/# Configuration pages
│   ├── history/        # History management pages
│   ├── manual/         # User manual pages
│   ├── rs-queue/       # RS queue management
│   ├── setting/        # System settings pages
│   └── task-list/      # Task list management
├── services/           # API services and data fetching
│   └── api/           # RTK Query API definitions
├── store/              # Redux state management
│   └── reducers/      # Redux slice definitions
├── styles/             # CSS stylesheets
│   ├── components/    # Component-specific styles
│   ├── pages/         # Page-specific styles
│   └── utils/         # Utility styles
├── tests/              # Test files
├── typings/            # TypeScript type definitions
└── utils/              # Utility functions and helpers
```

## Naming Conventions

- **Components**: PascalCase (e.g., `UserCard.tsx`)
- **Hooks**: useCamelCase (e.g., `useFetch.ts`)
- **Files/Directories**: kebab-case for general files
- **Variables/Functions**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Types/Interfaces**: PascalCase

## File Organization Patterns

### Components
- Each major component has its own directory
- Index files export main components
- Related components grouped together (e.g., Button/ButtonGroup)

### Pages
- Organized by feature/route
- Each page directory contains:
  - Main page component
  - Page-specific components in `components/` subdirectory
  - Page-specific hooks in `hooks/` subdirectory
  - Types and utilities as needed

### API Services
- Organized by domain/feature in `services/api/modules/`
- Each module contains API endpoints and types
- Base configuration in `services/api/base/`

### Redux Store
- Slice-based organization in `store/reducers/`
- Each feature has its own slice
- Centralized store configuration

## Key Architectural Patterns

- **Feature-based organization**: Pages and related code grouped by business feature
- **Separation of concerns**: Clear separation between UI, state, and API layers
- **Reusable components**: Shared components in dedicated directories
- **Type safety**: Comprehensive TypeScript definitions in `typings/`
- **Testing co-location**: Tests organized to mirror source structure

## Import Patterns

- Use `@/` alias for absolute imports from src
- React imports first, then external libraries, then internal modules
- Alphabetical ordering within import groups
- Separate import groups with newlines