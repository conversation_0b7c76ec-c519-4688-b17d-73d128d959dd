# Technology Stack

## Core Technologies

- **Frontend Framework**: React 18.2 with TypeScript
- **Build Tool**: Vite 4.4 (requires Node.js 16+)
- **UI Framework**: Ant Design 5.x with dark theme
- **Routing**: React Router 7.x
- **State Management**: Redux Toolkit with RTK Query for API calls
- **Styling**: CSS + Ant Design theme customization
- **Internationalization**: i18next with react-i18next
- **Testing**: Vitest + React Testing Library + MSW (Mock Service Worker)
- **Code Quality**: ESLint with Airbnb TypeScript configuration

## Key Dependencies

- **Drag & Drop**: @dnd-kit suite for sortable interactions
- **WebSocket**: react-use-websocket for real-time communication
- **File Processing**: xlsx for Excel parsing, jszip for file compression
- **Utilities**: lodash-es, use-debounce
- **State Persistence**: redux-persist for maintaining state across sessions

## Development Commands

```bash
# Install dependencies
npm install

# Start development server (runs on port 3000)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Build and preview
npm run build:preview

# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Generate test coverage
npm run coverage

# Lint code
npm run lint
```

## Build Configuration

- **TypeScript**: Strict mode enabled with ES2020 target
- **Path Aliases**: `@/*` maps to `src/*`
- **SVG Support**: vite-plugin-svgr for importing SVGs as React components
- **Development**: MSW enabled for API mocking in development mode
- **Testing**: jsdom environment with global test utilities

## Browser Compatibility

- **Minimum Chrome**: 88+ (for CSS :where selector support)
- **CSS Features**: Logical properties support (Chrome 89+)
- **Fallbacks**: StyleProvider with hashPriority and transformers for older browsers

## Deployment

- **Docker**: Dockerfile included for containerized deployment
- **Nginx**: Configuration files for production serving
- **Port**: Default production port 80, development port 3000