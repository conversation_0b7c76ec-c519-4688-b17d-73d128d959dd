<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="/js/config.js"></script>
    <script>
      const basicEnv = {
        systemTitle: {
          head: "EFAI AutoSeg V3",
          autoSeg: "AutoSeg V3",
        },
        api: {
          rest: "/api",
          websocket: "/websocket"
        },
        UDI: "",
        serialNumber: "",
        manual: {
          isShow: true,
          fileName: "RT_user_manual.xlsx",
          sheet: {
            FAQ: "FAQ",
            direction: "說明書",
          },
        },
        configPage: {
          marginValueLimit: 80,
        },
        about: {
          fileName: "RT_about.xlsx",
          sheet: {
            container: "Container",
          },
        },
        demo: false,
        newConfigSetting: true,
        rending: "shine",
      };

      window.config = { env: Object.assign(basicEnv, env) };
      // update title
      (function () {
        document.title = window.config.env.systemTitle.head;
      })();
    </script>
    <title>EFAI AutoSeg</title>
    <link rel="stylesheet" href="/styles.css" />
  </head>
  <body>
    <style>
      body {
        margin: 0;
        overscroll-behavior-y: none;
      }
    </style>
    <div id="root-rending">
      <div class="img-container">
        <img src="/images/logo.svg" class="logo-img" alt="Loading..." />
        <div class="shine-overlay"></div>
      </div>
    </div>
    <script>
      const loadingEl = document.getElementById("root-rending"); // DOM is ready
      if (loadingEl) {
        loadingEl.classList.add(window.config.env.rending);
      }
    </script>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
