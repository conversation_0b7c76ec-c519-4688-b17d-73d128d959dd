import { useEffect, useState } from 'react'

import { CollapseProps } from 'antd'
import { Link } from 'react-router'

import { fetchManualExcel } from 'src/utils/fetchExcelData'

type Items = CollapseProps['items']
type Hook = () => Items

const useParseFAQ: Hook = () => {
  const [jsonData, setJsonData] = useState<Items>([])

  const parseContent = (content: string): (string | JSX.Element)[] => {
    const regex = /(<a href='.*?'>.*?<\/a>)/g
    const parts = content.split(regex).filter(Boolean)

    return parts.map((part, index) => {
      const key = `link-${index + 1}`
      if (regex.test(part)) {
        const match = part.match(/<a href='(.*?)'>(.*?)<\/a>/)

        if (match) {
          return (
            <Link
              key={key}
              to={match[1]}
              target="_blank"
              className="directions"
            >
              {match[2]}
            </Link>
          )
        }
      }
      return <span key={key}>{part}</span>
    })
  }

  useEffect(() => {
    (async function fn() {
      const data = await fetchManualExcel('FAQ')
      const items: Items = data.map((item, index) => ({
        key: index + 1,
        label: item.question,
        children: <p>{parseContent(item.answer)}</p>,
        className: 'FAQ-panel',
      }))
      setJsonData(items)
    }())
  }, [])

  return jsonData
}

export default useParseFAQ
