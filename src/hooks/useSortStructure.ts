import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { updateDetail, updateStructureSort } from 'src/store/reducers/detailSlice'

const useSortStructure = () => {
  // redux
  const {
    detail: {
      structures,
      customized_structures: customizedStructures,
    },
    structureSort,
  } = useAppSelector((state) => state.detailReducer)
  const structureConfig = useAppSelector((state) => state.configReducer.structureConfig)
  const dispatch = useAppDispatch()

  // function
  const updateSort = async () => {
    const structureIds = new Set(structures.map(({ id }) => id))
    const customizedStructureIds = new Set(customizedStructures.map(({ id }) => id))

    const preSort: { id: number, customized: boolean }[] = []
    const existedStructureId = new Set()
    const existedCustomizedStructureId = new Set()
    // original sort
    structureSort
      .slice()
      .sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0))
      .forEach(({ id, customized }) => {
        if ((customized && customizedStructureIds.has(id))) {
          existedCustomizedStructureId.add(id)
          preSort.push({ id, customized })
        } else if (structureIds.has(id)) {
          existedStructureId.add(id)
          preSort.push({ id, customized })
        }
      })
    // create new sort
    const newSort = [
      ...structures
        .filter(({ id }) => !existedStructureId.has(id))
        .sort((a, b) => a.id - b.id)
        .map(({ id }) => ({ id, customized: false })),
      ...customizedStructures
        .filter(({ id, show }) => show && !existedCustomizedStructureId.has(id))
        .reverse()
        .map(({ id }) => ({ id, customized: true })),
    ]

    // integrate two sort and data
    const structureMap = new Map(structureConfig.map((item) => [item.id, item]))
    const customizedStructureMap = new Map(customizedStructures.map((item) => [item.id, item]))

    const integrateSort = await Promise.all(
      [...newSort, ...preSort].map(async ({ id, customized }, index) => {
        const sourceMap = customized ? customizedStructureMap : structureMap
        const foundItem = sourceMap.get(id)
        const sort = index + 1

        if (!foundItem) return { mergedSort: {}, dataSource: {} }

        const name = customized && 'name' in foundItem
          ? (foundItem as CustomizedStructuresType).name || ''
          : (foundItem as StructureConfigType).customized_name || (foundItem as StructureConfigType).efai_structure_name

        return {
          mergedSort: { id, sort, customized },
          dataSource: {
            id: foundItem.id,
            name,
            color_code: foundItem.color_code,
            sort,
            ...(customized && 'mode' in foundItem ? { mode: foundItem.mode } : {}),
          },
        }
      }),
    )

    const mergedSort = integrateSort.map((item) => item.mergedSort)
    const dataSource = integrateSort.map((item) => item.dataSource)

    if (newSort.length) {
      dispatch(updateStructureSort((mergedSort)))
    }
    return { mergedSort, dataSource }
  }

  const updateSortAfterSendApi = async () => {
    const { mergedSort } = await updateSort()

    const { structureSortMap, customizedStructureSortMap } = mergedSort.reduce(
      (acc, item) => {
        if (!item) return acc
        const { id, sort, customized } = item;
        (customized ? acc.customizedStructureSortMap : acc.structureSortMap).set(id, sort)
        return acc
      },
      { structureSortMap: new Map(), customizedStructureSortMap: new Map() },
    )

    const updatedStructures = structures.map((item) => ({
      id: item.id,
      sort: structureSortMap.get(item.id) ?? item.sort,
    }))

    const updatedCustomizedStructures = customizedStructures.map((item) => ({
      ...item,
      sort: customizedStructureSortMap.get(item.id) ?? item.sort,
    }))

    dispatch(updateDetail({
      structures: updatedStructures,
      customized_structures: updatedCustomizedStructures,
    }))

    return { updatedStructures, updatedCustomizedStructures }
  }

  return {
    updateSort,
    updateSortAfterSendApi,
  }
}

export default useSortStructure
