import {
  useRef, useState,
  type RefObject, type Dispatch, type SetStateAction,
} from 'react'

import type { ModalProps as AntdModalProps, ButtonProps as AntdButtonProps } from 'antd'

import modalFooterButtonProps from 'src/components/Modal/modalFooterButtonProps'

export interface TriggerProps<T> extends Omit<AntdButtonProps, 'onClick'> {
  onClick?: (value?: T) => void
}

type SetBoolean = Dispatch<SetStateAction<boolean>>
type FooterButtonHandlers<T> = (value: T, setOpen: SetBoolean, setConfirmLoading: SetBoolean) => void

interface ModalProps<T> extends Omit<AntdModalProps, 'onOk' | 'onCancel'> {
  onOk?: FooterButtonHandlers<T>
  onCancel?: FooterButtonHandlers<T>
  render?: (value: T) => React.ReactNode
}

interface ModalPropsWithReturn extends Omit<AntdModalProps, 'onOk' | 'onCancel'> {
  onOk?: () => void
  onCancel?: () => void
}

type ModalHooksProps<T> = {
  triggerProps?: TriggerProps<T>
  modalProps?: ModalProps<T>
} | undefined

type ModalHooksReturn<T> = {
  triggerProps: TriggerProps<T> & {
    ref: RefObject<HTMLButtonElement>,
  };
  modalProps: ModalPropsWithReturn,
  trigger: (value?: T, props?: ModalProps<T>) => void;
  dismiss: () => void;
  setOpen: SetBoolean
  setModalProps: Dispatch<SetStateAction<ModalProps<T>>>
}

export function useAntModal<T>({
  triggerProps, modalProps,
}: ModalHooksProps<T> = {}): ModalHooksReturn<T> {
  const [open, setOpen] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [data, setData] = useState<T>()
  const [modal, setModal] = useState<ModalProps<T>>({ ...modalProps })
  const triggerRef = useRef<HTMLButtonElement>(null)

  const trigger = (value?: T, props?: ModalProps<T>) => {
    setModal({ ...modalProps, ...props })
    setOpen(true)
    setData(value)
  }

  const onOpenChange = async (value?: T) => {
    try {
      await triggerProps?.onClick?.(value)
      trigger(data)
    } catch (error) {
      // error
    }
  }

  const dismiss = () => {
    setOpen(false)
    triggerRef.current?.focus()
  }

  const onOk = async () => {
    setConfirmLoading(true)
    let isSuccess = false
    try {
      await modalProps?.onOk?.(data as T, setOpen, setConfirmLoading)
      isSuccess = true
    } finally {
      setConfirmLoading(false)
      if (isSuccess) setOpen(false)
    }
  }

  const onCancel = async () => {
    await modalProps?.onCancel?.(data as T, setOpen, setConfirmLoading)
    setOpen(false)
  }

  return {
    triggerProps: {
      ...triggerProps,
      ref: triggerRef,
      onClick: onOpenChange,
    },
    modalProps: {
      ...modal,
      okButtonProps: {
        ...modalFooterButtonProps.okButtonProps,
        ...modal?.okButtonProps,
      },
      cancelButtonProps: {
        ...modalFooterButtonProps.cancelButtonProps,
        ...modal?.cancelButtonProps,
      },
      onOk,
      onCancel,
      open,
      confirmLoading: modal?.confirmLoading ? confirmLoading : undefined,
      children: modal?.render?.(data as T) || modal?.children,
    },
    trigger,
    dismiss,
    setOpen,
    setModalProps: setModal,
  }
}

export type {
  TriggerProps as ButtonProps, ModalHooksProps, ModalHooksReturn, FooterButtonHandlers,
}
