import { useState } from 'react'

import useWebSocket, { ReadyState } from 'react-use-websocket'

import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { kickOff } from 'src/store/reducers/authSlice'
import { setMessage } from 'src/store/reducers/websocketSlice'

function useWebSocketManager() {
  const [manualDisconnection, setManualDisconnection] = useState(false)
  const { token } = useAppSelector((state) => state.authReducer)
  const dispatch = useAppDispatch()

  const { sendJsonMessage, readyState, getWebSocket } = useWebSocket(window.config.env.api.websocket, {
    onOpen: () => console.log('[WebSocket] connected.'),
    onMessage: (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)
        if (data.code === 200) {
          dispatch(setMessage(data.content))
          sendJsonMessage({ token })
        } else {
          getWebSocket()?.close()
          setManualDisconnection(true)
          dispatch(kickOff())
        }
      } catch (err) {
        console.error('Failed to parse WebSocket message:', err)
      }
    },
    onClose: () => console.log('[WebSocket] closed.'),
    shouldReconnect: () => !manualDisconnection,
    reconnectInterval: 3000,
  })

  return {
    readyState,
    isConnected: readyState === ReadyState.OPEN,
  }
}

export default useWebSocketManager
