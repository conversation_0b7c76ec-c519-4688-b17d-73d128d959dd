import {
  entries, isArray, upperFirst, values,
} from 'lodash-es'

import i18n from '@/i18n'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { ConfigRequiredType, updateConfigRequired } from 'src/store/reducers/configSlice'
import { generateErrorMessage, GenerateErrorMessageParams } from 'src/utils/helper'
import { verifyDetailState } from 'src/utils/verify'

import useAlert from './useAlert'

const useInvalidField = (detail: WorklistDetailType | ProtocolDetailType | RsTemplateItemDetailType) => {
  // redux
  const dispatch = useAppDispatch()
  const { structureConfig } = useAppSelector((state) => state.configReducer)
  // hooks
  const handleAlert = useAlert()

  const structureIds = detail.customized_structures.map((item) => item.id)

  const customizedStructures = verifyDetailState.customizedStructures({
    custom: detail.customized_structures,
    origin: structureConfig,
  }, structureIds)

  const generatProtocolRules = (item: ProtocolDetailType): Partial<ConfigRequiredType> => {
    return {
      name: verifyDetailState.required(item.protocol_name),
      source: verifyDetailState.remote(item.source),
      study_info: verifyDetailState.studyInfo(item.study_info),
      structures: !item.structures.length,
      destination: verifyDetailState.destination(item.destination),
      structure_set_label: verifyDetailState.required(item.structure_set_label),
      customized_structures: customizedStructures,
    }
  }

  const generatTemplateRules = (item: RsTemplateItemDetailType): Partial<ConfigRequiredType> => {
    return {
      name: verifyDetailState.required(item.name),
      destination: verifyDetailState.destination(item.destination),
      structure_set_label: verifyDetailState.required(item.rs_set_label),
      source: false,
      structures: false,
      study_info: false,
      customized_structures: customizedStructures,
    }
  }

  const generatWorklistRules = (item: WorklistDetailType): Partial<ConfigRequiredType> => {
    return {
      name: false,
      study_info: false,
      structure_set_label: false,
      source: false,
      destination: false,
      structures: !item.structures.length,
      customized_structures: customizedStructures,
    }
  }

  const generateRules = (): [Partial<ConfigRequiredType>, string] => {
    if ('protocol_name' in detail) {
      return [generatProtocolRules(detail), upperFirst(i18n.t('plain_texts.protocol_name'))]
    }
    if ('name' in detail) {
      return [generatTemplateRules(detail), upperFirst(i18n.t('plain_texts.template_name'))]
    }
    return [generatWorklistRules(detail), '']
  }

  const verifyDetail = async () => {
    const [rules, name] = generateRules()
    const invalidFields = values(rules)
      .filter((value) => (isArray(value) ? value.length : value))

    dispatch(updateConfigRequired({ rules }))

    if (invalidFields.length === 0) return

    //  handle error
    const content = generateErrorMessage(entries(rules) as GenerateErrorMessageParams[], name)

    handleAlert({
      content,
      closable: true,
      footer: null,
      centered: true,
    }, 'Modal', 'confirm')
    throw new Error('throw new Error')
  }

  return { verifyDetail }
}

export default useInvalidField
