import { App, type ModalFuncProps } from 'antd'

import { AlertCircle, AlertCircleRed } from 'src/assets/icons'
import i18n from 'src/i18n'

import 'src/styles/components/modal.css'

type Type = 'Msg' | 'Modal'
type Status = 'warning' | 'error' | 'success' | 'info' | 'confirm'

function useAlert() {
  const { modal: antModal } = App.useApp()

  const msg = {
    success: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.success({
        className: 'useAlert useAlert-msg',
        icon: <AlertCircle width={28} height={28} />,
        ...modalFnProps,
      })
    },
    info: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.info({
        className: 'useAlert useAlert-msg',
        closeIcon: 'X',
        icon: <AlertCircle width={28} height={28} />,
        ...modalFnProps,
      })
    },
    warning: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.warning({
        className: 'useAlert useAlert-msg',
        icon: <AlertCircleRed width={28} height={28} />,
        ...modalFnProps,
      })
    },
    error: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.error({
        title: i18n.t('error_titles.'),
        content: i18n.t('error_contents.server_error'),
        className: 'useAlert useAlert-msg',
        icon: <AlertCircleRed width={28} height={28} />,
        ...modalFnProps,
      })
    },
    confirm: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.info({
        className: 'useAlert useAlert-msg',
        closeIcon: 'X',
        icon: <AlertCircle width={28} height={28} />,
        ...modalFnProps,
      })
    },
  }

  const modal = {
    success: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.success({
        className: 'useAlert useAlert-modal',
        icon: null,
        ...modalFnProps,
      })
    },
    info: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.info({
        className: 'useAlert useAlert-modal',
        icon: null,
        ...modalFnProps,
      })
    },
    warning: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.warning({
        className: 'useAlert useAlert-modal',
        icon: null,
        ...modalFnProps,
      })
    },
    error: ({ ...modalFnProps }: ModalFuncProps) => {
      antModal.error({
        className: 'useAlert useAlert-modal',
        icon: null,
        ...modalFnProps,
      })
    },
    confirm: ({ styles, ...modalFnProps }: ModalFuncProps) => {
      antModal.confirm({
        className: 'useAlert useAlert-modal',
        icon: null,
        width: 350,
        styles: {
          ...styles,
        },
        ...modalFnProps,
      })
    },
  }

  return (
    modalFnProps: ModalFuncProps,
    type: Type = 'Msg',
    status: Status = 'info',
  ) => {
    if (type === 'Msg') return msg[status](modalFnProps)
    if (type === 'Modal') return modal[status](modalFnProps)
    return () => { }
  }
}

export default useAlert
