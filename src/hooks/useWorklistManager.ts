// /* 待廢棄 */
// import { useEffect, useState } from 'react'

// import { Form } from 'antd'
// import type { SorterResult } from 'antd/es/table/interface'

// import useAlert from 'src/hooks/useAlert'
// import {
//   useReGetWorklistMutation,
// } from 'src/services/api'
// import { useAppSelector, useAppDispatch } from 'src/store/hook'
// import {
//   resetWorklistState,
//   updateCurrentPage,
//   updateWorklistFocus,
//   updateWorklistGroupSearch,
//   clearWorklist,
// } from 'src/store/reducers/worklistSlice'

// interface SearchFormValues {
//   patientId?: string
//   studyStatus?: string
//   pickDate?: [{ format: (format: string) => string }, { format: (format: string) => string }] | null
// }

// interface SorterInfo {
//   field?: string
//   order?: 'ascend' | 'descend'
// }

// interface FormInstance {
//   getFieldsValue: () => SearchFormValues
//   resetFields: () => void
// }

// /**
//  * Extract and format form values for API requests
//  */
// export function extractFormValues(form: FormInstance) {
//   const formValues = form.getFieldsValue()
//   return {
//     patientId: formValues.patientId,
//     studyStatus: formValues.studyStatus,
//     studyDateRangeStart: formValues.pickDate?.[0]?.format('YYYY-MM-DD HH:00'),
//     studyDateRangeEnd: formValues.pickDate?.[1]?.format('YYYY-MM-DD HH:00'),
//   }
// }

// /**
//  * Calculate sorting parameters for API requests
//  */
// export function calculateSortingParams(
//   page: number | undefined,
//   sorter: SorterInfo | undefined,
//   currentSearch: {
//     order_key: string
//     ascend: boolean
//   },
// ) {
//   const nextPage = page || 1
//   let nextOrderKey = ''

//   // Page undefined means changing sorter, order undefined means cancel the sorter
//   if (!page) {
//     nextOrderKey = sorter && sorter.order ? sorter.field || '' : ''
//   } else {
//     nextOrderKey = currentSearch.order_key
//   }

//   const nextOrder = sorter ? sorter.order === 'ascend' : currentSearch.ascend

//   return { nextPage, nextOrderKey, nextOrder }
// }

// export function useWorklistManager(isHistoryPage: boolean = false) {
//   const [form] = Form.useForm()

//   // Data and API operations
//   const dispatch = useAppDispatch()
//   const handleAlert = useAlert()

//   const {
//     worklistGroupFocus,
//     worklistGroupPage,
//     worklistGroupSearch,
//   } = useAppSelector((state) => state.worklistReducer)

//   const { message } = useAppSelector((state) => state.websocketReducer)

//   const [getWorklistGroupMutation] = useGetWorklistGroupMutation()
//   const [reGetWorklistGroupMutation] = useReGetWorklistGroupMutation()
//   const [reGetWorklistMutation] = useReGetWorklistMutation()

//   // Table state
//   const [groupTableLoading, setGroupTableLoading] = useState<boolean>(false)
//   const [tableLoading, setTableLoading] = useState<boolean>(false)
//   const [groupSorted, setGroupSorted] = useState<SorterResult<WorklistGroupType>>({})
//   const [worklistSorted, setWorklistSorted] = useState<SorterResult<SeriesType>>({})
//   const [historySorted, setHistorySorted] = useState<SorterResult<HistoryWorklistType>>({})

//   const getWorklistGroup = async (page?: number, sorter?: SorterInfo) => {
//     setGroupTableLoading(true)

//     try {
//       const formValues = extractFormValues(form)
//       const { nextPage, nextOrderKey, nextOrder } = calculateSortingParams(
//         page,
//         sorter,
//         worklistGroupSearch,
//       )

//       await getWorklistGroupMutation({
//         page: nextPage,
//         patient_id: formValues.patientId,
//         study_status: formValues.studyStatus,
//         study_date_range_start: formValues.studyDateRangeStart,
//         study_date_range_end: formValues.studyDateRangeEnd,
//         order_key: nextOrderKey,
//         ascend: nextOrder,
//         history: isHistoryPage,
//       }).unwrap()

//       if (sorter) {
//         setGroupSorted(sorter)
//       }

//       dispatch(updateCurrentPage({ page: nextPage }))
//       dispatch(updateWorklistGroupSearch({
//         patient_id: formValues.patientId,
//         study_status: formValues.studyStatus,
//         study_date_range_start: formValues.studyDateRangeStart,
//         study_date_range_end: formValues.studyDateRangeEnd,
//         order_key: nextOrderKey,
//         ascend: nextOrder,
//       }))
//     } catch (error) {
//       const errorMessage = error && typeof error === 'object' && 'data' in error
//         ? (error as Err).data?.detail
//         : 'An error occurred'
//       handleAlert({ content: errorMessage }, 'Msg', 'error')
//     } finally {
//       setGroupTableLoading(false)
//     }
//   }

//   const reGetWorklistGroup = async () => {
//     try {
//       await reGetWorklistGroupMutation({
//         page: worklistGroupPage.current,
//         patient_id: worklistGroupSearch.patient_id,
//         study_status: worklistGroupSearch.study_status,
//         study_date_range_start: worklistGroupSearch.study_date_range_start,
//         study_date_range_end: worklistGroupSearch.study_date_range_end,
//         order_key: worklistGroupSearch.order_key,
//         ascend: worklistGroupSearch.ascend,
//         history: isHistoryPage,
//       }).unwrap()
//     } catch (error) {
//       // Don't use useAlert for polling requests
//       console.error('Failed to refresh worklist group:', error)
//     }
//   }

//   const reGetWorklist = async (worklistGroupId: number) => {
//     setTableLoading(true)

//     try {
//       await reGetWorklistMutation({ worklist_group_id: worklistGroupId }).unwrap()
//     } catch (error) {
//       const errorMessage = error && typeof error === 'object' && 'data' in error
//         ? (error as Err).data?.detail
//         : 'An error occurred'
//       handleAlert({ content: errorMessage }, 'Msg', 'error')
//     } finally {
//       setTableLoading(false)
//     }
//   }

//   // Effects
//   useEffect(() => {
//     getWorklistGroup()

//     return () => {
//       form.resetFields()
//       setGroupSorted({})
//       setWorklistSorted({})
//       setHistorySorted({})
//       dispatch(updateWorklistFocus({ id: undefined }))
//       dispatch(resetWorklistState())
//     }
//   }, [])

//   useEffect(() => {
//     if (worklistGroupFocus.id) {
//       reGetWorklist(worklistGroupFocus.id)
//     } else {
//       dispatch(clearWorklist())
//     }
//   }, [worklistGroupFocus.timeStamp])

//   useEffect(() => {
//     if (message.content === 'worklist_update') {
//       reGetWorklistGroup()
//     }
//   }, [message.timestamp])

//   return {
//     form,
//     isHistoryPage,
//     groupTableLoading,
//     tableLoading,
//     groupSorted,
//     worklistSorted,
//     setWorklistSorted,
//     historySorted,
//     setHistorySorted,
//     getWorklistGroup,
//   }
// }
