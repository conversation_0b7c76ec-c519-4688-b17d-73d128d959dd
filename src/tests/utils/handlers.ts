// eslint-disable-next-line import/no-extraneous-dependencies
import { rest } from 'msw'

import { deleteNotify, getNotifty } from '../mock/notify'
import { resendRemote } from '../mock/reducersData'
import { getListGroup, worklistFocus } from '../mock/worklistData'

const url = 'http://test/api'
const success = { status: 200 }

const handlers = [
  rest.get(`${url}/login/token/refresh`, (_, res, ctx) => {
    return res(
      ctx.status(success.status),
      ctx.json({
        token: 'token-123',
      }),
    )
  }),
  rest.get(`${url}/notify`, (req, res, ctx) => {
    const newUrl = new URL(req.url)
    const quantity = Number(newUrl.searchParams.get('quantity'))

    return res(
      ctx.status(success.status),
      ctx.json(getNotifty(quantity)),
      ctx.delay(150),
    )
  }),
  rest.delete(`${url}/notify`, (_, res, ctx) => res(
    ctx.status(success.status),
    ctx.json(deleteNotify),
    ctx.delay(150),
  )),
  rest.delete(`${url}/login`, (_, res, ctx) => {
    return res(
      ctx.status(success.status),
      ctx.json({
        result: 'OK',
      }),
    )
  }),
  rest.get(`${url}/task/*`, (_, res, ctx) => res(
    ctx.status(404),
    ctx.json({
      detail: 'RS dicom not found.',
    }),
  )),
  rest.post(`${url}/task/*`, (_, res, ctx) => res(
    ctx.status(404),
    ctx.json({
      detail: 'RS dicom not found.',
    }),
  )),
  rest.get(`${url}/source_destination`, (_, res, ctx) => res(
    ctx.status(200),
    ctx.delay(150),
    ctx.json(resendRemote),
  )),
  rest.get(`${url}/worklist`, (_, res, ctx) => res(
    ctx.status(200),
    ctx.delay(150),
    ctx.json(worklistFocus),
  )),
  rest.get(`${url}/worklist/group`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.delay(150),
      ctx.json(getListGroup),
    )
  }),
  rest.patch(`${url}/worklist/group/*`, (_, res, ctx) => res(
    ctx.status(200),
    ctx.json({
      result: 'OK',
    }),
    ctx.delay(150),
  )),
]

export default handlers
