import { render } from '@testing-library/react'

import { parseMenu } from 'src/utils/parseManual'

describe('parseMenu', () => {
  it('should parse h1 elements correctly', async () => {
    const data: BaseElement[] = [
      { tag: 'h1', children: 'Title 1' },
    ]

    const result = await parseMenu(data)

    const { getByText } = render(result.items[0].title)
    expect(getByText('Title 1')).toBeInTheDocument()

    expect(result).toEqual({
      items: [
        {
          key: 'H1-Title 1-0',
          href: '#H1-Title 1-0',
          title: expect.any(Object), // 因為這是一個 JSX 元素，所以不進行深度比較
        },
      ],
      defaultOpenKeys: ['H1-Title 1-0'],
    })
  })

  it('should parse h2 elements correctly', async () => {
    const data: BaseElement[] = [
      { tag: 'h2', children: 'Title 2' },
    ]

    const result = await parseMenu(data)

    const { getByText } = render(result.items[0].title)
    expect(getByText('Title 2')).toBeInTheDocument()

    expect(result).toEqual({
      items: [
        {
          key: 'H2-Title 2-0',
          href: '#H2-Title 2-0',
          title: expect.any(Object),
        },
      ],
      defaultOpenKeys: ['H2-Title 2-0'],
    })
  })

  it('should handle mixed heading levels correctly', async () => {
    const data: BaseElement[] = [
      { tag: 'h1', children: 'Title 1' },
      { tag: 'div', children: 'Not a heading' },
      { tag: 'h2', children: 'Title 2' },
      { tag: 'p', children: 'Not a heading' },
      { tag: 'h3', children: 'Title 3' },
    ]

    const result = await parseMenu(data)

    expect(result.items.length).toBe(3)
    expect(result.defaultOpenKeys.length).toBe(3)
  })
})
