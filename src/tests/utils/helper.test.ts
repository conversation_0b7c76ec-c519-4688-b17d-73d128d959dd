import {
  checkDicomTagConform,
  formattedStatus,
  generateErrorMessage,
  sortDate,
  sortSpecificCase,
  removeId,
} from 'src/utils/helper'

type RulesList = [string, boolean]

describe('helper.ts', () => {
  it('sortSpecificCase', () => {
    const statusOver = ['a', 'b', 'c']
    expect(sortSpecificCase('a', 'b', statusOver)).toBe(-1)
    expect(sortSpecificCase('b', 'a', statusOver)).toBe(1)
    expect(sortSpecificCase('a', 'a', statusOver)).toBe(0)
    expect(sortSpecificCase('', '', statusOver)).toBe(0)
    expect(sortSpecificCase('z', 'x', statusOver)).toBe(0)
  })

  it('sortDate', () => {
    expect(sortDate('2023-9-10', '2023-10-10')).toBe(-1)
    expect(sortDate('2023-9-10', '2023-9-10')).toBe(0)
    expect(sortDate('2023-9-10', '2023-8-10')).toBe(1)
  })

  it('formattedStatus', () => {
    expect(formattedStatus('NO_MATCH')).toBe('No Match')
    expect(formattedStatus('ABCDEF')).toBe('Abcdef')
    expect(formattedStatus('TEST_TEST')).toBe('Test_test')
  })

  it('checkDicomTagConform', () => {
    expect(checkDicomTagConform('(1111,2222)')).toBeFalsy()
    expect(checkDicomTagConform('(1111,222)')).toBeTruthy()
    expect(checkDicomTagConform('(111,2222)')).toBeTruthy()
    expect(checkDicomTagConform('1111,2222')).toBeTruthy()
    expect(checkDicomTagConform('11112222')).toBeTruthy()
  })

  it('generateErrorMessage', () => {
    // Arrange
    const errorList1: RulesList[] = [
      ['ae_title', false],
      ['all', false],
      ['add_customized_structures', true],
    ]
    const errorList2: RulesList[] = [
      ['ae_title', true],
      ['all', true],
      ['add_customized_structures', false],
    ]

    // Assert
    expect(generateErrorMessage(errorList1)).toBe('At least 1 "Add Empty Structures" is required.')
    expect(generateErrorMessage(errorList2)).toBe('"AE Title", "All" are all required.')
  })

  it('removeId', () => {
    expect(removeId([
      {
        dicom_tag: '0000000x', format: 'regex', value: '21515653', id: 0,
      },
      {
        dicom_tag: 'xczxxczx', format: 'exact', value: '', id: 1,
      },
    ])).toStrictEqual([
      {
        dicom_tag: '0000000x', format: 'regex', value: '21515653',
      },
      {
        dicom_tag: 'xczxxczx', format: 'exact', value: '',
      },
    ])
  })
})
