import { parseElements } from 'src/utils/parseManual'

describe('parseElements', () => {
  it('should parse unordered list items correctly', async () => {
    const data: ManualJSONData[] = [
      { tag: 'ul', content: 'Item 1' },
      { content: 'Item 2' },
    ]

    const result = await parseElements(data)
    expect(result).toEqual([
      {
        tag: 'ul',
        children: [
          { tag: 'li', children: 'Item 1' },
          { tag: 'li', children: 'Item 2' },
        ],
      },
    ])
  })

  it('should parse image elements correctly', async () => {
    const data: ManualJSONData[] = [
      { tag: 'img', content: 'Image content', figcaption: 'Caption' },
    ]

    const result = await parseElements(data)

    expect(result).toEqual([
      { tag: 'figure', children: 'Image content', figcaption: 'Caption' },
    ])
  })

  it('should handle mixed content correctly', async () => {
    const data: ManualJSONData[] = [
      { tag: 'img', content: 'Image content', figcaption: 'Caption' },
      { tag: 'ul', content: 'Item 1' },
      { tag: 'div', content: 'Some content' },
    ]

    const result = await parseElements(data)

    expect(result).toEqual([
      { tag: 'figure', children: 'Image content', figcaption: 'Caption' },
      { tag: 'ul', children: [{ tag: 'li', children: 'Item 1' }] },
      { tag: 'div', children: 'Some content' },
    ])
  })

  it('should handle mixed content correctly when last element is an list', async () => {
    const data: ManualJSONData[] = [
      { tag: 'img', content: 'Image content', figcaption: 'Caption' },
      { tag: 'div', content: 'Some content' },
      { tag: 'ul', content: 'Item 1' },
      { tag: 'ul', content: 'Item 2' },
      { tag: 'ul', content: 'Item 3' },
    ]

    const result = await parseElements(data)

    expect(result).toEqual([
      { tag: 'figure', children: 'Image content', figcaption: 'Caption' },
      { tag: 'div', children: 'Some content' },
      {
        tag: 'ul',
        children: [
          { tag: 'li', children: 'Item 1' },
          { tag: 'li', children: 'Item 2' },
          { tag: 'li', children: 'Item 3' },
        ],
      },
    ])
  })
})
