import '@testing-library/jest-dom'
import { fetch, Headers, Request, Response } from 'cross-fetch'
import { setupServer } from "msw/node"


import handlers from './utils/handlers'

// window
global.fetch = fetch
global.Headers = Headers
global.Request = Request
global.Response = Response
global.window.config = {
  env: {
    systemTitle: {
      head: '',
      autoSeg: '',
    },
    api: {
      rest: 'http://test/api',
      websocket: 'http://test/ws',
    },
    UDI: `*+123*`,
    serialNumber: `EFAI_RT`,
    manual: {
      isShow: true,
      fileName: 'manual.pdf',
      sheet: {
        direction: 'directions',
        FAQ: 'FAQ'
      }
    },
    configPage: {
      marginValueLimit: 80,
    },
    about: {  // !!news
      fileName: 'RT_about.xlsx',
      sheet: {
        container: 'Container',
      },
    },
    demo: false, // !!news

  }
}

// i18n
vi.mock('next-i18next', () => ({
  useTranslation: () => ({
    t: vi.fn().mockImplementation((key) => key),
    i18n: { language: 'en-us' },
  }),
}))

// Server init
export const server = setupServer(...handlers)

beforeAll(() => {
  server.listen()
})

beforeEach(() => {
  vi.spyOn(window, 'matchMedia').mockImplementation(() => {
    return {
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    } as unknown as MediaQueryList
  })
})

afterEach(() => {
  server.resetHandlers()
})

afterAll(() => {
  server.close()
})