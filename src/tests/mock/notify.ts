import { Notify } from 'src/store/reducers/notifySlice'

const notifyList = (count: number): NotifyType[] => {
  const list: NotifyType[] = []
  for (let index = 1; index <= count; index++) {
    list.push({
      id: index + 1,
      type: 'inference failed',
      patient_id: `test-card-${index}`,
      message: 'There was an issue processing at least one of the series.',
      time: '2023-11-16 08:32:59',
    })
  }

  return list
}

const notiftyInitialState: Notify = {
  totalNotify: 55,
  notifyQuantity: 10,
  notifyList: notifyList(10),
}
export const deleteNotify: Notify = {
  totalNotify: 0,
  notifyQuantity: 10,
  notifyList: [],
}

export const getNotifty = (count: number) => {
  return {
    total_notify: 56,
    notify_list: notifyList(count),
  }
}

export default notiftyInitialState
