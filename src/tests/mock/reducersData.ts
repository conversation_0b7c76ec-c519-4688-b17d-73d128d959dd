export const resendRemote = {
  remote_server_list: [
    {
      id: '41',
      name: 'test-41',
      type: 'DESTINATION',
    },
    {
      id: '32',
      name: 'test-32',
      type: 'DESTINATION',
    },
    {
      id: '24',
      name: 'test-24',
      type: 'SOURCE',
    },
    {
      id: '23',
      name: 'test-23',
      type: 'SOURCE',
    },
    {
      id: '20',
      name: 'test-20',
      type: 'SOURCE',
    },
    {
      id: '19',
      name: 'test-19',
      type: 'SOURCE',
    },
    {
      id: '6',
      name: 'test-6',
      type: 'DESTINATION',
    },
    {
      id: '4',
      name: 'test-4',
      type: 'DESTINATION',
    },
    {
      id: '3',
      name: 'test-3',
      type: 'SOURCE',
    },
    {
      id: '2',
      name: 'test-2',
      type: 'DESTINATION',
    },
  ],
  folder_list: [
    {
      id: '16',
      name: 'test-16',
      type: 'DESTINATION',
    },
    {
      id: '15',
      name: 'test-15',
      type: 'SOURCE',
    },
    {
      id: '11',
      name: 'test-11',
      type: 'DESTINATION',
    },
  ],
}

export const configInitialState = {
  remoteServerConfig: resendRemote.remote_server_list,
  folderConfig: resendRemote.folder_list,
  structureConfig: [],
  protocolConfig: [],
  customizedStructureType: [],
  configRequired: {
    protocol_name: false,
    structure_set_label: false,
    source: false,
    destination: false,
    structures: false,
    study_info: false,
    customized_structures: false,
  },
}
