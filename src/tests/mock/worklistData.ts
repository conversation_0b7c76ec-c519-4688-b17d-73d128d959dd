import { Worklist } from 'src/store/reducers/worklistSlice'

const pendingWorklistGroup: WorklistGroupType[] = [
  {
    id: 1,
    insert: true,
    patient_id: '01',
    study_date: '2022-09-11',
    study_status: 'PENDING',
    progress: 0.0,
  },
  {
    id: 2,
    insert: true,
    patient_id: '02',
    study_date: '2022-09-12',
    study_status: 'PENDING',
    progress: 1.0,
  },
]

export const suspendedWorklistGroup: WorklistGroupType[] = [
  {
    id: 1,
    insert: true,
    patient_id: '01',
    study_date: '2022-09-11',
    study_status: 'SUSPENDED',
    progress: 0.0,
  },
  {
    id: 2,
    insert: true,
    patient_id: '02',
    study_date: '2022-09-12',
    study_status: 'PENDING',
    progress: 1.0,
  },
]

export const processingWorklistGroup: WorklistGroupType[] = [
  {
    id: 1,
    insert: true,
    patient_id: '01',
    study_date: '2022-09-11',
    study_status: 'PROCESSING',
    progress: 0.0,
  },
  {
    id: 2,
    insert: true,
    patient_id: '02',
    study_date: '2022-09-12',
    study_status: 'PENDING',
    progress: 1.0,
  },
]

export const getListGroup = {
  current_page: 1,
  page_size: 10,
  total_data: 15,
  worklist_group: pendingWorklistGroup,
}

export const worklistReducer: Worklist = {
  worklistGroup: suspendedWorklistGroup,
  worklistGroupFocus: {
    id: undefined,
    timeStamp: undefined,
  },
  worklistGroupPage: {
    current: 1,
    total: 15,
    size: 10,
  },
  worklistGroupSearch: {
    patient_id: undefined,
    study_status: undefined,
    study_date_range_start: undefined,
    study_date_range_end: undefined,
    order_key: '',
    ascend: false,
  },
  worklist: {
    series_count: 1,
    study_description: '23',
    last_modified: '2023-10-18',
    series: [{
      worklist_id: 23,
      number: 4,
      time: '2023-09-12 17:07:34',
      last_modified: '2023-10-18 09:44:01',
      protocol_name: 'EFAI_tt55512312312',
      image: 100,
      status: 'SUSPENDED',
      description: 'Scout',
    }],
  },
  worklistDetail: {
    patient_id: '',
    structure_set_label: '',
    study_date: '',
    study_description: '',
    use_protocol: null,
    protocol_description: '',
    series_time: '',
    series_description: '',
    destination: {
      remote_server: [],
      folder: [],
    },
    structures: [],
    customized_structures: [],
  },
  updateWorklistDetailCheck: false,
  resendRemote: {
    destination: {
      remote_server: [{ id: 1 }, { id: 2 }],
      folder: [{ id: 1 }],
    },
  },
}

export const worklistFocus = {
  series_count: 1,
  study_description: '123',
  last_modified: '2023-10-18',
  series: [
    {
      worklist_id: 23,
      number: 4,
      time: '2023-09-12 17:07:34',
      last_modified: '2023-10-18 09:44:01',
      protocol_name: 'EFAI_tt55512312312',
      image: 100,
      status: 'SUSPENDED',
      description: 'Scout',
    },
  ],
}
