import { fireEvent, render, screen } from '@testing-library/react'

import { FooterBtnGroup } from 'src/components/Modal'
import { saveButton } from 'src/utils/saveButton'

describe('FooterBtnGroup.tsx', () => {
  it('type = button ', () => {
    const btnClick = vi.fn()
    render(<FooterBtnGroup items={[
      {
        type: 'button',
        btnName: 'cancel',
        btnClass: 'outline',
        btnClick,
      },
    ]}
    />)

    const cancel = screen.getByRole('button', {
      name: 'cancel',
    })

    fireEvent.click(cancel)
    expect(btnClick).toBeCalledTimes(1)
  })
  it('modal onOk', () => {
    const btnClick = vi.fn()
    const onOk = vi.fn()
    const onCancel = vi.fn()
    render(<FooterBtnGroup items={[
      {
        ...saveButton,
        btnName: 'open',
        btnClick,
        onOk,
        onCancel,
      },
    ]}
    />)

    const openBtn = screen.getByRole('button', {
      name: /open/i,
    })
    fireEvent.click(openBtn)
    expect(btnClick).toBeCalledTimes(1)

    const modal = screen.getByRole('dialog')
    expect(modal).toBeInTheDocument()

    const okBtn = screen.getByRole('button', {
      name: /Save/i,
    })
    fireEvent.click(okBtn)
    expect(onOk).toBeCalledTimes(1)
  })
  it('modal onCancel', () => {
    const btnClick = vi.fn()
    const onOk = vi.fn()
    const onCancel = vi.fn()
    render(<FooterBtnGroup items={[
      {
        ...saveButton,
        btnName: 'open',
        btnClick,
        onOk,
        onCancel,
      },
    ]}
    />)

    const openBtn = screen.getByRole('button', {
      name: /open/i,
    })
    fireEvent.click(openBtn)

    const modal = screen.getByRole('dialog')
    expect(modal).toBeInTheDocument()

    const cancelBtn = screen.getByRole('button', {
      name: /Cancel/i,
    })
    fireEvent.click(cancelBtn)
    expect(onCancel).toBeCalledTimes(1)
  })
})
