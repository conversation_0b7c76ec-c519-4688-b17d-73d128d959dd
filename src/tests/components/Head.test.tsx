import { waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { Head } from 'src/components/Head'
import renderWithProviders from 'src/tests/utils/renderWithProviders'

const auth = {
  isAuth: false,
  rememberAccount: 'test user',
  token: 'user',
  username: 'user',
  _persist: {
    version: 1,
    rehydrated: false,
  },
}

describe('Head', () => {
  it('user menu', async () => {
    // Arrange
    const { container, getByRole } = renderWithProviders(<Head>Head</Head>, {
      preloadedState: {
        authReducer: auth,
      },
    })
    const dropDownButton = getByRole('button', { name: /user/i })
    expect(dropDownButton).toBeInTheDocument()

    // Act
    userEvent.hover(dropDownButton)

    // Assert
    await waitFor(() => getByRole('button', { name: /logout/i }))
    expect(getByRole('button', { name: /logout/i })).toBeInTheDocument()

    await userEvent.click(getByRole('button', { name: /logout/i }))
    expect(container).toMatchSnapshot()
  })
})
