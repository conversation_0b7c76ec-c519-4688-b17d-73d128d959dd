// eslint-disable-next-line import/no-extraneous-dependencies
import { fireEvent, render, screen } from '@testing-library/react'

import Button from 'src/components/Button'

type ButtonTypes = {
  type: 'button' | 'submit' | 'reset'
}

const buttonTypes: ButtonTypes[] = [
  { type: 'button' },
  { type: 'submit' },
  { type: 'reset' },
]

describe('button testing', () => {
  const handleCallback = vi.fn()

  afterEach(() => {
    // cleanup on exiting
    handleCallback.mockClear()
  })

  it('render button ', () => {
    const content = 'Hi'
    render(<Button>{content}</Button>)
    const element = screen.getByRole('button')
    expect(element).toBeInTheDocument()
    expect(element).toHaveTextContent(content)
    expect(element).toMatchSnapshot()
  })

  it('onClick', () => {
    render(<Button onClick={handleCallback}>Click Me</Button>)
    const element = screen.getByRole('button')

    fireEvent.click(element)
    expect(handleCallback).toHaveBeenCalled()
    expect(element).toMatchSnapshot()
  })

  it('disable', () => {
    const element = render(
      <Button onClick={handleCallback} disabled>
        disable
      </Button>,
    ).getByRole('button')
    fireEvent.click(element)
    expect(handleCallback).not.toHaveBeenCalled()
    expect(element).toMatchSnapshot()
  })
  it.each(buttonTypes)('button htmlType', ({ type }) => {
    render(
      <Button type={type} disabled={type === 'reset'}>
        {type}
      </Button>,
    )
    screen.debug()
    const element = screen.getByRole('button')
    expect(element).toMatchSnapshot()
    expect(element).toHaveAttribute('type', type)
    expect(element).toHaveTextContent(type)
  })
})
