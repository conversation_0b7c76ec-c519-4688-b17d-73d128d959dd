import {
  fireEvent, render, renderHook, screen, waitFor,
} from '@testing-library/react'
import { Form } from 'antd'

import { SearchList } from 'src/components/List'

const optionList = [
  {
    value: 'start',
    label: 'start',
  },
  {
    value: 'pause',
    label: 'pause',
  },
  {
    value: 'finish',
    label: 'finish',
  },
]

describe('SearchList.tsx', () => {
  const handleSearch = vi.fn()

  beforeEach(() => {
    const { result } = renderHook(() => Form.useForm())
    vi.spyOn(Form, 'useForm').mockImplementation(() => result.current)

    handleSearch.mockClear()
  })

  it('Props', async () => {
    // Arrange
    const [form] = Form.useForm()
    render(
      <SearchList
        form={form}
        handleSearch={handleSearch}
        optionList={optionList}
      />,
    )

    // Act
    const searchInput = screen.getByPlaceholderText('Search Patient Id ...')
    fireEvent.input(searchInput, {
      target: { value: '123' },
    })

    const button = screen.getByRole('button', {
      name: 'Search',
    })
    fireEvent.click(button)

    // Assert
    await waitFor(() => {
      expect(handleSearch).toHaveBeenCalledTimes(1)
    })
  })

  it('deleteInputValue', async () => {
    // Arrange
    const [form] = Form.useForm()
    const { container } = render(
      <SearchList
        form={form}
        handleSearch={handleSearch}
        optionList={optionList}
        datePicker
      />,
    )

    const searchInput = screen.getByPlaceholderText('Search Patient Id ...')
    const selectBox = screen.getByRole('combobox')

    fireEvent.input(searchInput, {
      target: { value: 'zxc' },
    })
    fireEvent.change(selectBox, {
      target: { value: 'finish' },
    })

    expect(searchInput).toHaveValue('zxc')

    // Act
    const button = screen.getByRole('button', {
      name: 'Search',
    })
    fireEvent.click(button)

    await waitFor(() => {
      expect(handleSearch).toHaveBeenCalledTimes(1)
    })

    // Assert
    const deleteInputIcon = container.querySelector('.ant-space-item:first-child .ant-input-clear-icon') as Element
    expect(deleteInputIcon).toBeInTheDocument()
    fireEvent.click(deleteInputIcon)

    await waitFor(() => expect(handleSearch).toHaveBeenCalledTimes(2))

    const deleteSelectIcon = container.querySelector('.ant-space-item:nth-child(2) .ant-select-clear') as Element
    fireEvent.mouseDown(deleteSelectIcon)
    await waitFor(() => expect(handleSearch).toHaveBeenCalledTimes(3))
  })
})
