import { render, screen } from '@testing-library/react'

import ProgressBar from 'src/components/ProgressBar'

const statusList = [
  {
    status: 'PROCESSING',
    title: 'Processing',
    progress: 50,
  },
  {
    status: 'PENDING',
    title: 'Pending',
    progress: 0,
  },
  {
    status: 'SUSPENDED',
    title: 'Suspended',
    progress: 99,
  },
]

describe('ProgressBar', () => {
  it.each(statusList)('ProgressBar Props', (item) => {
    render(<ProgressBar status={item.status} progress={item.progress} />)

    const title = screen.getByText(item.title)
    expect(title).toBeInTheDocument()
    expect(screen).toMatchSnapshot()
  })
})
