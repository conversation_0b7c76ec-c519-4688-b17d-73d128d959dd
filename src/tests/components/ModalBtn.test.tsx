import { fireEvent, render, screen } from '@testing-library/react'

import { ModalBtn } from 'src/components/Modal'

describe('ModalBtn', async () => {
  const btnName = 'Open'
  const modalTitle = 'Test Modal'
  const okText = 'OK'

  const list: ModalBtnType[] = [
    {
      btnName,
      btnType: 'button',
      btnClass: 'gray',
      btnClick: vi.fn(),
      modalTitle,
      modalContent: 'Modal',
      okText,
      onOk: vi.fn(),
      onCancel: vi.fn(),
      width: 350,
      btnDisabled: false,
      tooltip: 'tooltip',
    },
    {
      btnName,
      btnType: 'submit',
      btnClass: 'class',
      btnClick: undefined,
      modalTitle,
      modalContent: 'Modal',
      okText,
      onCancel: undefined,
      width: 350,
      btnDisabled: false,
      tooltip: 'tooltip',
    },
    {
      btnName,
      btnType: 'reset',
      btnClass: 'class',
      btnClick: vi.fn(),
      modalTitle,
      modalContent: 'Modal',
      okText,
      onOk: vi.fn(),
      onCancel: vi.fn(),
      width: 350,
      btnDisabled: true,
      modalFooter: null,
      tooltip: 'tooltip',
    },
  ]

  beforeEach(() => {
    vi.fn().mockClear()
  })

  it.each(list)('onOk', async (item) => {
    const { container } = render(<ModalBtn {...item} />)

    const button = screen.getByRole('button', {
      name: /Open/i,
    })
    fireEvent.click(button)

    if (item.btnDisabled) {
      expect(item.btnClick).toHaveBeenCalledTimes(0)
      expect(container).toMatchSnapshot()
    } else {
      const okButton = screen.getByRole('button', {
        name: /OK/i,
      })
      if (item.btnClick !== undefined) {
        expect(item.btnClick).toHaveBeenCalledTimes(1)
      }
      expect(container).toBeInTheDocument()

      expect(okButton).toBeInTheDocument()
      fireEvent.click(okButton)

      if (item.onOk) expect(item.onOk).toHaveBeenCalledTimes(1)

      expect(container).toMatchSnapshot()
    }
  })

  it.each(list)('onCancel', (item) => {
    const { container } = render(<ModalBtn {...item} />)
    const button = screen.getByRole('button', {
      name: /Open/i,
    })
    expect(container).toMatchSnapshot()
    fireEvent.click(button)

    if (item.btnDisabled) {
      if (item.btnClick !== undefined) {
        expect(item.btnClick).toHaveBeenCalledTimes(0)
        expect(container).toMatchSnapshot()
      } else {
        expect(container).toBeInTheDocument()
      }
    } else {
      const modal = screen.getByRole('dialog')
      const cancelButton = screen.getByRole('button', {
        name: /Cancel/i,
      })
      if (item.btnClick !== undefined) expect(item.btnClick).toHaveBeenCalledTimes(2)
      expect(modal).toMatchSnapshot()

      fireEvent.click(cancelButton)
      if (item.onCancel !== undefined) expect(item.onCancel).toHaveBeenCalledTimes(1)

      expect(modal).toMatchSnapshot()
    }
  })
})
