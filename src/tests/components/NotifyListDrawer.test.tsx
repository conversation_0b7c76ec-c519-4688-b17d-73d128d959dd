import {
  fireEvent, screen, waitFor,
} from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import NotifyListDrawer from 'src/components/Head/NotifyListDrawer'
import renderWithProviders from 'src/tests/utils/renderWithProviders'

import notiftyInitialState from '../mock/notify'

describe('NotifyListDrawer.tsx', () => {
  const user = userEvent.setup()
  it('notify open and close', async () => {
    // Arrange
    const { container, getByRole, queryByRole } = renderWithProviders(<NotifyListDrawer />, {
      preloadedState: {
        notifyReducer: notiftyInitialState,
      },
    })
    const notifyButton = getByRole('button', { name: /5 5/i })

    // open notify Drawer
    fireEvent.click(notifyButton)

    expect(getByRole('heading', { name: /notice/i })).toBeInTheDocument()
    const close = getByRole('button', { name: /close/i })
    expect(container).toMatchSnapshot()
    // close notify Drawer
    fireEvent.click(close)
    expect(queryByRole('heading', { name: /notice/i })).not.toBeInTheDocument()
    expect(container).toMatchSnapshot()
  })

  it('notify cards count and show more', async () => {
    // Arrange
    const {
      getByRole, getAllByText, findAllByText, findByRole,
    } = renderWithProviders(<NotifyListDrawer />)
    const openNotify = await screen.findByRole('button', { name: /5 6/i })
    user.click(openNotify)

    // have notice card
    expect(await findByRole('heading', { name: /notice/i })).toBeInTheDocument()

    // expect(getByText('test-card-10')).toBeInTheDocument()
    const testCard = await findAllByText(/test-card/i)

    expect(testCard).toHaveLength(10)

    // handle show more
    const showMore = getByRole('button', { name: /show more/i })
    await user.click(showMore)
    await waitFor(() => {
      expect(getAllByText(/patient id/i)).toHaveLength(20)
    })
  })
})
