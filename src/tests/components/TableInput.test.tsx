import {
  fireEvent, render, screen, waitFor,
} from '@testing-library/react'

import { TableInput } from 'src/components/Form'

describe('tableInput.tsx ', () => {
  const handleChange = vi.fn()

  beforeEach(() => {
    handleChange.mockClear()
  })

  it('props', async () => {
    const { getByRole } = render(<TableInput
      id={1}
      name="name"
      value="hallo world"
      handleChange={handleChange}
    />)

    expect(getByRole('textbox')).toHaveValue('hallo world')
    screen.debug()
  })

  it('error', async () => {
    const { rerender } = render(<TableInput
      id={1}
      name="name"
      handleChange={handleChange}
      required
    />)

    const requiredError = screen.getByText('Please enter name')
    expect(requiredError).toBeInTheDocument()

    fireEvent.input(screen.getByRole('textbox'), {
      target: { value: 'Hi' },
    })

    await waitFor(() => {
      expect(screen.getByRole('textbox')).toHaveValue('Hi')
    })
    expect(handleChange).toBeCalledTimes(1)

    rerender(<TableInput
      id={1}
      name="name"
      handleChange={handleChange}
      duplication
      value="1234"
    />)

    const repeatError = screen.getByText('Name must all be unique')
    expect(repeatError).toBeInTheDocument()

    rerender(<TableInput
      id={1}
      name="name"
      handleChange={handleChange}
      duplication
      errorText="error"
    />)

    const errorText = screen.getByText('error')
    expect(errorText).toBeInTheDocument()
  })
})
