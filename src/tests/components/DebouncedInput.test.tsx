// DebouncedWithHOC.test.tsx
import React from 'react'

import { render, screen, fireEvent } from '@testing-library/react'
import { Input } from 'antd'
import { vi } from 'vitest'

import DebouncedWithHOC from 'src/components/DebouncedWithHOC'

describe('DebouncedWithHOC', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
  })

  test('renders children with initial default value', () => {
    const handleChange = vi.fn()
    render(
      <DebouncedWithHOC defaultValue="initial" onChange={handleChange}>
        <Input />
      </DebouncedWithHOC>,
    )
    const input = screen.getByRole('textbox')
    expect(input).toHaveValue('initial')
  })

  test('updates value and calls onChange after debounce', () => {
    const handleChange = vi.fn()
    render(
      <DebouncedWithHOC defaultValue="start" onChange={handleChange} delay={500}>
        <Input />
      </DebouncedWithHOC>,
    )
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'updated' } })
    expect(input).toHaveValue('updated')
    // onChange should not be called immediately
    expect(handleChange).not.toHaveBeenCalled()
    // Fast-forward debounce time
    vi.advanceTimersByTime(500)
    expect(handleChange).toHaveBeenCalledWith('updated')
  })

  test('syncs controlled value prop changes', () => {
    const handleChange = vi.fn()
    const { rerender } = render(
      <DebouncedWithHOC defaultValue="default" value="controlled" onChange={handleChange}>
        <Input />
      </DebouncedWithHOC>,
    )
    const input = screen.getByRole('textbox')
    expect(input).toHaveValue('controlled')
    // Update value prop
    rerender(
      <DebouncedWithHOC defaultValue="default" value="new controlled" onChange={handleChange}>
        <Input />
      </DebouncedWithHOC>,
    )
    expect(input).toHaveValue('new controlled')
  })
})
