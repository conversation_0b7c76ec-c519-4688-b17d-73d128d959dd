import { fireEvent, render, screen } from '@testing-library/react'

import StructuresTag from 'src/components/Tag/StructuresTag'

describe('structures-tag', () => {
  const tagData = {
    id: 1,
    efai_structure_name: 'tag-001',
    customized_name: 'customized-tag-001',
    color_code: '#c0c0c0',
    category: [],
    tag: [],
  }

  const handleChange = vi.fn()
  const isEqual = (num: number) => num === tagData.id

  beforeEach(() => {
    handleChange.mockClear()
  })

  test('component props', () => {
    const num: number = 0
    const { container, rerender } = render(
      <StructuresTag
        tagData={tagData}
        checked={isEqual(num)}
        onChange={handleChange}
        seeColor
      />,
    )
    const text = screen.getByText('tag-001')
    expect(text).toHaveTextContent('tag-001')

    expect(container.querySelector('.tag-checkable-checked-icon')).not.toBeInTheDocument()
    expect(isEqual(num)).toBe(false)

    rerender(
      <StructuresTag
        tagData={tagData}
        checked={isEqual(1)}
        onChange={handleChange}
        seeColor={false}
      />,
    )
    expect(container.querySelector('.tag-checkable-checked-icon')).toBeInTheDocument()
    expect(isEqual(num)).toBe(false)
  })

  test('handleUpdateStructures', () => {
    const num: number = 0
    const newNum = (number: number) => num + number
    const { container } = render(
      <StructuresTag
        tagData={tagData}
        checked={isEqual(num)}
        onChange={() => newNum(2)}
        seeColor
        closeIcon={false}
      />,
    )
    const element: Element = container.querySelector('span.structure-tag') as Element
    fireEvent.click(element)
    expect(element).toHaveClass('ant-tag-checkable')
    expect(newNum(2)).toBe(2)
  })
})
