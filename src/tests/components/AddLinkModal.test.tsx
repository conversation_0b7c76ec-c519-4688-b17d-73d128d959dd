import React from 'react'

import {
  fireEvent, render, screen,
} from '@testing-library/react'

import { AddLinkModal } from 'src/components/Modal'

describe('AddLinkModal.tsx', () => {
  const open: boolean = true

  const setOpenModal = vi.fn()

  beforeEach(() => {
    setOpenModal.mockClear()
  })

  it('onCancel', async () => {
    const onOk = vi.fn()

    const { container, getByRole } = render(
      <AddLinkModal
        modalLinkBtn={onOk}
        setOpenModal={setOpenModal}
        openModal={open}
      />,
    )
    const onCancel = getByRole('button', {
      name: /Cancel/i,
    })

    fireEvent.click(onCancel)

    expect(container).toBeInTheDocument()
    expect(setOpenModal).toBeCalledTimes(1)
  })

  it('visitor', async () => {
    const mockModalLinkBtn = vi.fn().mockResolvedValue(1)

    const { getByRole, getByDisplayValue } = render(
      <AddLinkModal
        modalLinkBtn={mockModalLinkBtn}
        setOpenModal={setOpenModal}
        openModal={open}
      />,
    )

    const visitorRadio = getByDisplayValue(/visitor/i)
    const okButton = getByRole('button', {
      name: 'Link',
    })

    expect(visitorRadio).toHaveProperty('checked', true)

    fireEvent.click(okButton)

    mockModalLinkBtn()

    expect(mockModalLinkBtn).toBeCalledTimes(1)
    // expect(setOpenModal).toBeCalled()
  })

  it('registered user', async () => {
    const mockModalLinkBtn = vi.fn().mockResolvedValue(1)

    const {
      getByRole, getByDisplayValue, getByLabelText,
    } = render(
      <AddLinkModal
        modalLinkBtn={mockModalLinkBtn}
        setOpenModal={setOpenModal}
        openModal={open}
      />,
    )
    const registeredUser = getByDisplayValue(/registeredUser/i)

    const okButton = getByRole('button', {
      name: 'Link',
    })
    const userName = screen.getByPlaceholderText(/username/i)
    const password = getByLabelText(/password/i)
    // const data = {}

    fireEvent.click(registeredUser)
    expect(registeredUser).toHaveProperty('checked', true)

    fireEvent.input(userName, {
      target: {
        value: 'test',
      },
    })

    fireEvent.input(password, {
      target: {
        value: '123',
      },
    })

    fireEvent.click(okButton)

    mockModalLinkBtn()

    expect(mockModalLinkBtn).toBeCalledTimes(1)
  })
})
