import {
  fireEvent, render, screen,
} from '@testing-library/react'

import ColorPicker from 'src/components/Popup/ColorPicker'

describe('ColorDownOutlined.tsx', () => {
  it('props', () => {
    const handleChange = vi.fn()
    const { container, rerender, getByRole } = render(
      <ColorPicker
        value="f0f0f0"
        onChange={handleChange}
      />,
    )
    const colorPicker = container.querySelector('.ant-color-picker-trigger') as Element
    fireEvent.click(colorPicker)

    const tooltip = screen.getByRole('tooltip', { hidden: true })
    expect(tooltip).toBeInTheDocument()

    fireEvent.change(getByRole('textbox'), {
      target: { value: '039ba0' },
    })
    expect(getByRole('textbox')).toHaveValue('f0f0f0')
    expect(handleChange).toBeCalledTimes(1)

    rerender(<ColorPicker
      onChange={handleChange}
    />)
    fireEvent.click(colorPicker)

    expect(getByRole('textbox')).toHaveValue('039ba0')
  })
})
