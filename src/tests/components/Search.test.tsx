import {
  fireEvent, render, renderHook, screen, waitFor,
} from '@testing-library/react'
import { Form } from 'antd'

import { Search } from 'src/components/Form'

describe('Search.tsx', () => {
  const handleSearch = vi.fn()

  beforeEach(() => {
    handleSearch.mockClear()
  })

  it('form', async () => {
    const { result } = renderHook(() => Form.useForm())
    vi.spyOn(Form, 'useForm').mockImplementation(() => result.current)
    const [form] = Form.useForm()

    const { container } = render(<Search form={form} handleSearch={handleSearch} />)

    const input = screen.getByPlaceholderText('Search Name')
    fireEvent.input(input, {
      target: {
        value: 'test',
      },
    })
    const button = screen.getByRole('button', {
      name: /search/i,
    })

    fireEvent.click(button)
    await waitFor(() => {
      expect(handleSearch).toBeCalledTimes(1)
    })
    expect(input).toHaveValue('test')

    const deletebutton = container.querySelector('svg') as Element

    fireEvent.click(deletebutton)

    const newInput = screen.getByPlaceholderText('Search Name')
    expect(newInput).not.toHaveValue('test')
  })
})
