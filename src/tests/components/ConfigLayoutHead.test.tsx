import { fireEvent, screen, within } from '@testing-library/react'

import { ConfigLayoutHead } from 'src/components/Head'
import renderWithProviders from 'src/tests/utils/renderWithProviders'

describe(' ConfigLayoutHead.tsx ', () => {
  it('navigatePage', () => {
    renderWithProviders(<ConfigLayoutHead navigatePage="/">Test_001</ConfigLayoutHead>)
    const heading = screen.getByRole('heading', { name: /Test_001/i })
    const backBtn = within(heading).getByRole('button')
    fireEvent.click(backBtn)
  })
  it('no navigatePage', () => {
    const { container } = renderWithProviders(<ConfigLayoutHead>Test_001</ConfigLayoutHead>)
    const heading = screen.getByRole('heading', { name: /Test_001/i })
    const backBtn = within(heading).getByRole('button')
    fireEvent.click(backBtn)
    expect(container).toMatchSnapshot()
  })
})
