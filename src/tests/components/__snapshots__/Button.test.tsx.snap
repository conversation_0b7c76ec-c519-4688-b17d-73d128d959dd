// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`button testing > button htmlType 1`] = `
<button
  class="btn undefined"
  type="button"
>
  button
</button>
`;

exports[`button testing > button htmlType 2`] = `
<button
  class="btn undefined"
  type="submit"
>
  submit
</button>
`;

exports[`button testing > button htmlType 3`] = `
<button
  class="btn undefined"
  disabled=""
  type="reset"
>
  reset
</button>
`;

exports[`button testing > disable 1`] = `
<button
  class="btn undefined"
  disabled=""
  type="button"
>
  disable
</button>
`;

exports[`button testing > onClick 1`] = `
<button
  class="btn undefined"
  type="button"
>
  Click Me
</button>
`;

exports[`button testing > render button  1`] = `
<button
  class="btn undefined"
  type="button"
>
  Hi
</button>
`;
