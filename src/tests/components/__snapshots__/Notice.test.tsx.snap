// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Notice Card > iconType 1`] = `
<div>
  <section
    class="notice-card"
  >
    <button
      class="ant-btn css-dev-only-do-not-override-cdzvx5 ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm close-btn"
      type="button"
    >
      <span>
         
      </span>
      <svg
        fill="none"
        height="16"
        viewBox="0 0 16 16"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          id="x-circle"
        >
          <path
            d="M12 4L4 12"
            id="Vector"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4 4L12 12"
            id="Vector_2"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </g>
      </svg>
    </button>
    <main
      class="card-body"
    >
      <div
        class="card-icon-box"
      >
        <svg
          fill="none"
          height="28"
          viewBox="0 0 28 28"
          width="28"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            id="ok-circle"
          >
            <path
              d="M14.7 25.9C20.8856 25.9 25.9 20.8856 25.9 14.7C25.9 8.51441 20.8856 3.5 14.7 3.5C8.51441 3.5 3.5 8.51441 3.5 14.7C3.5 20.8856 8.51441 25.9 14.7 25.9Z"
              fill="#8CC88E"
              id="Vector"
            />
            <path
              d="M20.2988 11.3398L13.3688 18.2698L10.2188 15.1198"
              id="Vector_2"
              stroke="white"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </svg>
      </div>
      <div
        class="card-content"
      >
        <hgroup>
          <h5>
            Patient ID
          </h5>
          <p>
            inference_successed-1
          </p>
        </hgroup>
        <div
          class="card-text"
        >
          inference_successed-1
        </div>
      </div>
    </main>
    <footer
      class="card-footer"
    >
      2023-10-25
    </footer>
  </section>
</div>
`;

exports[`Notice Card > iconType 2`] = `
<div>
  <section
    class="notice-card"
  >
    <button
      class="ant-btn css-dev-only-do-not-override-cdzvx5 ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm close-btn"
      type="button"
    >
      <span>
         
      </span>
      <svg
        fill="none"
        height="16"
        viewBox="0 0 16 16"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          id="x-circle"
        >
          <path
            d="M12 4L4 12"
            id="Vector"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4 4L12 12"
            id="Vector_2"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </g>
      </svg>
    </button>
    <main
      class="card-body"
    >
      <div
        class="card-icon-box"
      >
        <svg
          fill="none"
          height="25"
          viewBox="0 0 26 25"
          width="26"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            id="alert-triangle"
          >
            <path
              d="M12.134 2C12.5189 1.33333 13.4811 1.33333 13.866 2L25.1244 21.5C25.5093 22.1667 25.0281 23 24.2583 23H1.74167C0.971868 23 0.490744 22.1667 0.875644 21.5L12.134 2Z"
              fill="#00B3B9"
              id="Polygon 3"
            />
            <ellipse
              cx="13.1336"
              cy="19.1074"
              fill="white"
              id="Ellipse 571"
              rx="0.705883"
              ry="0.75"
            />
            <path
              d="M13.168 9.5L13.1679 15.5"
              id="Vector"
              stroke="white"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </svg>
      </div>
      <div
        class="card-content"
      >
        <hgroup>
          <h5>
            Patient ID
          </h5>
          <p>
            inference_failed-2
          </p>
        </hgroup>
        <div
          class="card-text"
        >
          inference_failed-2
        </div>
      </div>
    </main>
    <footer
      class="card-footer"
    >
      2023-10-25
    </footer>
  </section>
</div>
`;

exports[`Notice Card > iconType 3`] = `
<div>
  <section
    class="notice-card"
  >
    <button
      class="ant-btn css-dev-only-do-not-override-cdzvx5 ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm close-btn"
      type="button"
    >
      <span>
         
      </span>
      <svg
        fill="none"
        height="16"
        viewBox="0 0 16 16"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          id="x-circle"
        >
          <path
            d="M12 4L4 12"
            id="Vector"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4 4L12 12"
            id="Vector_2"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </g>
      </svg>
    </button>
    <main
      class="card-body"
    >
      <div
        class="card-icon-box"
      >
        <svg
          fill="none"
          height="28"
          viewBox="0 0 16 16"
          width="28"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            id="alert-circle"
          >
            <path
              d="M8.4 14.8C11.9346 14.8 14.8 11.9346 14.8 8.4C14.8 4.86538 11.9346 2 8.4 2C4.86538 2 2 4.86538 2 8.4C2 11.9346 4.86538 14.8 8.4 14.8Z"
              fill="#E53D00"
              id="Vector"
              stroke="#E53D00"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.3999 5.3999L8.3999 9.3999"
              id="Vector_2"
              stroke="white"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.3999 11.96H8.4063"
              id="Vector_3"
              stroke="white"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </svg>
      </div>
      <div
        class="card-content"
      >
        System Disconnected
        <div
          class="card-text"
        >
          system_disconnected-3
        </div>
      </div>
    </main>
    <footer
      class="card-footer"
    >
      2023-10-25
    </footer>
  </section>
</div>
`;

exports[`Notice Card > iconType 4`] = `
<div>
  <section
    class="notice-card"
  >
    <button
      class="ant-btn css-dev-only-do-not-override-cdzvx5 ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm close-btn"
      type="button"
    >
      <span>
         
      </span>
      <svg
        fill="none"
        height="16"
        viewBox="0 0 16 16"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          id="x-circle"
        >
          <path
            d="M12 4L4 12"
            id="Vector"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4 4L12 12"
            id="Vector_2"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </g>
      </svg>
    </button>
    <main
      class="card-body"
    >
      <div
        class="card-icon-box"
      >
        <svg
          fill="none"
          height="29"
          viewBox="0 0 28 29"
          width="28"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            id="alert-circle"
          >
            <path
              d="M13.3 2.6C7.11441 2.6 2.1 7.61441 2.1 13.8C2.1 19.9856 7.11441 25 13.3 25C19.4856 25 24.5 19.9856 24.5 13.8C24.5 7.61441 19.4856 2.6 13.3 2.6Z"
              id="Vector"
              stroke="#00B3B9"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M13.3008 19.0498L13.3008 12.0498"
              id="Vector_2"
              stroke="#00B3B9"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M13.3008 7.57031L13.2896 7.57031"
              id="Vector_3"
              stroke="#00B3B9"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </svg>
      </div>
      <div
        class="card-content"
      >
        Exceeds File Limit
        <div
          class="card-text"
        >
          exceeds_file limit-4
        </div>
      </div>
    </main>
    <footer
      class="card-footer"
    >
      2023-10-25
    </footer>
  </section>
</div>
`;

exports[`Notice Card > iconType 5`] = `
<div>
  <section
    class="notice-card"
  >
    <button
      class="ant-btn css-dev-only-do-not-override-cdzvx5 ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-sm close-btn"
      type="button"
    >
      <span>
         
      </span>
      <svg
        fill="none"
        height="16"
        viewBox="0 0 16 16"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          id="x-circle"
        >
          <path
            d="M12 4L4 12"
            id="Vector"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4 4L12 12"
            id="Vector_2"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </g>
      </svg>
    </button>
    <main
      class="card-body"
    >
      <div
        class="card-icon-box"
      >
        <svg
          fill="none"
          height="28"
          viewBox="0 0 16 16"
          width="28"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            id="alert-circle"
          >
            <path
              d="M8.4 14.8C11.9346 14.8 14.8 11.9346 14.8 8.4C14.8 4.86538 11.9346 2 8.4 2C4.86538 2 2 4.86538 2 8.4C2 11.9346 4.86538 14.8 8.4 14.8Z"
              fill="#E53D00"
              id="Vector"
              stroke="#E53D00"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.3999 5.3999L8.3999 9.3999"
              id="Vector_2"
              stroke="white"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.3999 11.96H8.4063"
              id="Vector_3"
              stroke="white"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </g>
        </svg>
      </div>
      <div
        class="card-content"
      >
        System Maintenance
        <div
          class="card-text"
        >
          inference_timeout-5
        </div>
      </div>
    </main>
    <footer
      class="card-footer"
    >
      2023-10-25
    </footer>
  </section>
</div>
`;
