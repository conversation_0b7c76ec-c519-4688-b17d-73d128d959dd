// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ModalBtn > onCancel 1`] = `
<div>
  <button
    class="btn  gray"
    type="button"
  >
    Open
  </button>
</div>
`;

exports[`ModalBtn > onCancel 2`] = `
<div>
  <button
    class="btn outline class"
    type="submit"
  >
    Open
  </button>
</div>
`;

exports[`ModalBtn > onCancel 3`] = `
<div>
  <button
    class="btn outline class"
    disabled=""
    type="reset"
  >
    Open
  </button>
</div>
`;

exports[`ModalBtn > onCancel 4`] = `
<div>
  <button
    class="btn outline class"
    disabled=""
    type="reset"
  >
    Open
  </button>
</div>
`;

exports[`ModalBtn > onOk 1`] = `
<div>
  <button
    class="btn outline class"
    disabled=""
    type="reset"
  >
    Open
  </button>
</div>
`;
