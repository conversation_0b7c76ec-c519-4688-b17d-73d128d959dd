import { fireEvent, render } from '@testing-library/react'

import { Notice } from 'src/components/Modal'

type Icon = {
  type: 'inference successed' | 'inference failed' | 'system disconnected' | 'exceeds file limit' | 'inference timeout'
  id: number
  time: string
}

const iconType: Icon[] = [
  {
    type: 'inference successed',
    id: 1,
    time: '2023-10-25',
  },
  {
    type: 'inference failed',
    id: 2,
    time: '2023-10-25',
  },
  {
    type: 'system disconnected',
    id: 3,
    time: '2023-10-25',
  },
  {
    type: 'exceeds file limit',
    id: 4,
    time: '2023-10-25',
  },
  {
    type: 'inference timeout',
    id: 5,
    time: '2023-10-25',
  },
]

describe('Notice Card', () => {
  const handleClear = vi.fn()
  afterEach(() => {
    handleClear.mockClear()
  })
  test.each(iconType)('iconType', ({ type, id, time }) => {
    const isPatientId = (type === 'inference successed') || (type === 'inference failed')
    const { container } = render(<Notice
      id={id}
      type={type}
      time={time}
      patient_id={isPatientId ? `${type.replace(' ', '_')}-${id}` : undefined}
      msg={`${type.replace(' ', '_')}-${id}`}
      handleClear={handleClear}
    />)
    const closeButton = container.querySelector('.close-btn')

    if (closeButton) {
      fireEvent.click(closeButton)
      expect(handleClear).toHaveBeenCalled()
    }

    expect(container).toBeInTheDocument()
    expect(container).toMatchSnapshot()
  })
})
