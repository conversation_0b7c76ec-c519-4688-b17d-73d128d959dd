// Legacy mock data functions
function createHistoryItems(
  length: number,
): (isItem: boolean) => Array<HistoryGroupType & { items: HistoryGroupType[] }> {
  return (isItem: boolean) => Array.from({ length }, (_, index) => ({
    group_id: index,
    modality: 'CT' as const,
    images: 100,
    series_number: 1,
    series_description: 'Series Description',
    source: 'Source',
    status: 'SUCCEEDED',
    items: isItem ? createHistoryItems(3)(false) : [],
  }))
}

export function createHistoryWorklistMockData(): HistoryWorklistType[] {
  const data = Array.from({ length: 10 }, (_, index) => ({
    list_id: index,
    study_date: '2023-09-12',
    study_description: 'Study Description',
    modality: ['CT', 'RS'] as ModalityType[],
    group: createHistoryItems(index)(true),
  }))

  return data
}

// New API format mock data - 直接使用你提供的資料格式
export const newHistoryWorklistMockData: HistoryWorklistResponseType = {
  study: [
    {
      viewer_study_id: '1.2.840.113619.2.55.3.537526739.901.1289767590.75',
      study_date: '2023-09-12',
      study_description: '456',
      total_modality: ['CT', 'MR', 'RS'],
      series: [
        {
          viewer_series_id: '1.2.840.113619.2.55.3.537526739.901.1289767590.79',
          series_number: 2,
          series_description: 'BRAIN',
          instance_count: 0,
          worklist: [
            {
              id: 5,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
            {
              id: 62,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
            {
              id: 74,
              image_count: 250,
              status: 8,
              protocol: 1,
            },
          ],
        },
        {
          viewer_series_id: '1.2.840.113619.2.55.3.537526739.901.1289767590.77',
          series_number: 1,
          series_description: 'Brain Scout',
          instance_count: 0,
          worklist: [
            {
              id: 4,
              image_count: 250,
              status: 4,
              protocol: 1,
            },
            {
              id: 79,
              image_count: 250,
              status: 4,
              protocol: 1,
            },
            {
              id: 61,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
            {
              id: 78,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
          ],
        },
        {
          viewer_series_id: '1.2.840.113619.2.55.3.537526739.901.1289767590.78',
          series_number: 3,
          series_description: 'Brain Scout 3',
          instance_count: 0,
          worklist: [],
        },
      ],
    },
    {
      viewer_study_id: '1.3.840.113619.2.55.3.537526739.901.1289767590.75',
      study_date: '2023-09-12',
      study_description: '2234',
      total_modality: ['CT', 'RS'],
      series: [
        {
          viewer_series_id: '1.3.840.113619.2.55.3.537526739.901.1289767590.79',
          series_number: 25,
          series_description: 'BRAIN',
          instance_count: 0,
          worklist: [
            {
              id: 51,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
            {
              id: 621,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
            {
              id: 741,
              image_count: 250,
              status: 8,
              protocol: 1,
            },
          ],
        },
        {
          viewer_series_id: '1.3.840.113619.2.55.3.537526739.901.1289767590.78',
          series_number: 35,
          series_description: 'Brain Scout 3',
          instance_count: 0,
          worklist: [],
        },
        {
          viewer_series_id: '1.3.840.113619.2.55.3.537526739.901.1289767590.77',
          series_number: 15,
          series_description: 'Brain Scout',
          instance_count: 0,
          worklist: [
            {
              id: 41,
              image_count: 250,
              status: 4,
              protocol: 1,
            },
            {
              id: 791,
              image_count: 250,
              status: 4,
              protocol: 1,
            },
            {
              id: 611,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
            {
              id: 781,
              image_count: 250,
              status: 6,
              protocol: 1,
            },
          ],
        },
      ],
    },
  ],
}

export const historyWorklistMockData = createHistoryWorklistMockData()
