// RS Queue Group mock data (similar to worklist group)
export const mockRsQueueGroups: RsQueueGroupType[] = [
  {
    patient_id: '123456789',
    patient_name: '<PERSON> <PERSON>I',
    study_date: '2025-05-26',
    study_uid: '1.2.840.113619.2.5.**********.215519.978957063.78',
  },
  {
    patient_id: '987654321',
    patient_name: '<PERSON>',
    study_date: '2025-05-25',
    study_uid: '1.2.840.113619.2.5.**********.215519.978957063.79',
  },
  {
    patient_id: '456789123',
    patient_name: '<PERSON>',
    study_date: '2025-05-24',
    study_uid: '1.2.840.113619.2.5.**********.215519.978957063.80',
  },
  {
    patient_id: '789123456',
    patient_name: '<PERSON>',
    study_date: '2025-05-23',
    study_uid: '1.2.840.113619.2.5.**********.215519.978957063.81',
  },
  {
    patient_id: '321654987',
    patient_name: '<PERSON>',
    study_date: '2025-05-22',
    study_uid: '1.2.840.113619.2.5.**********.215519.978957063.82',
  },
]

// RS Queue items mock data (ImageRSList)
export const mockRsQueueItems: Record<number, ImageRSList[]> = {
  1: [
    {
      id: 101,
      rs_set_label: 'Liver Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:30:00',
    },
    {
      id: 102,
      rs_set_label: 'Lung Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:32:00',
    },
    {
      id: 103,
      rs_set_label: 'Heart Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:35:00',
    },
    {
      id: 104,
      rs_set_label: 'Kidney Segmentation',
      series_number: 4,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:38:00',
    },
    {
      id: 105,
      rs_set_label: 'Spleen Segmentation',
      series_number: 5,
      rs_set_date: '2024-01-15',
      import_time: '2024-01-15 14:40:00',
    },
  ],
  2: [
    {
      id: 201,
      rs_set_label: 'Brain Tumor Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-14',
      import_time: '2024-01-14 16:45:00',
    },
    {
      id: 202,
      rs_set_label: 'White Matter Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-14',
      import_time: '2024-01-14 16:47:00',
    },
    {
      id: 203,
      rs_set_label: 'Gray Matter Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-14',
      import_time: '2024-01-14 16:50:00',
    },
  ],
  3: [
    {
      id: 301,
      rs_set_label: 'Pancreas Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:20:00',
    },
    {
      id: 302,
      rs_set_label: 'Gallbladder Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:22:00',
    },
    {
      id: 303,
      rs_set_label: 'Stomach Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:25:00',
    },
    {
      id: 304,
      rs_set_label: 'Intestine Segmentation',
      series_number: 4,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:28:00',
    },
    {
      id: 305,
      rs_set_label: 'Bladder Segmentation',
      series_number: 5,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:30:00',
    },
    {
      id: 306,
      rs_set_label: 'Prostate Segmentation',
      series_number: 6,
      rs_set_date: '2024-01-13',
      import_time: '2024-01-13 10:32:00',
    },
  ],
  4: [
    {
      id: 401,
      rs_set_label: 'Cervical Spine Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:15:00',
    },
    {
      id: 402,
      rs_set_label: 'Thoracic Spine Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:17:00',
    },
    {
      id: 403,
      rs_set_label: 'Lumbar Spine Segmentation',
      series_number: 3,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:20:00',
    },
    {
      id: 404,
      rs_set_label: 'Spinal Cord Segmentation',
      series_number: 4,
      rs_set_date: '2024-01-12',
      import_time: '2024-01-12 18:22:00',
    },
  ],
  5: [
    {
      id: 501,
      rs_set_label: 'Brain Segmentation',
      series_number: 1,
      rs_set_date: '2024-01-11',
      import_time: '2024-01-11 12:30:00',
    },
    {
      id: 502,
      rs_set_label: 'Skull Segmentation',
      series_number: 2,
      rs_set_date: '2024-01-11',
      import_time: '2024-01-11 12:32:00',
    },
  ],
}

// Helper function to get RS queue items by group ID (using patient_id as key)
export const getRsQueueItemsByPatientId = (patientId: string): ImageRSList[] => {
  // Map patient_id to the mock data keys
  const patientIdToKey: Record<string, number> = {
    123456789: 1,
    987654321: 2,
    456789123: 3,
    789123456: 4,
    321654987: 5,
  }
  const key = patientIdToKey[patientId]
  return key ? mockRsQueueItems[key] || [] : []
}

// Helper function to filter RS queue groups by patient ID
export const filterRsQueueGroupsByPatientId = (patientId?: string): RsQueueGroupType[] => {
  if (!patientId) return mockRsQueueGroups
  return mockRsQueueGroups.filter((group) => group.patient_id.toLowerCase().includes(patientId.toLowerCase()))
}
