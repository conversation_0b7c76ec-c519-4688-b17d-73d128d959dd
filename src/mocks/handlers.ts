import { http, HttpResponse, delay } from 'msw'

import {
  mockRsQueueGroups,
  getRsQueueItemsByPatientId,
  filterRsQueueGroupsByPatientId,
} from './data/rs-queue.mock'
import { mockRsTemplates } from './data/rs-templates.mock'

const apiUrl = window.config.env.api.rest

// RS Queue API
const rsQueueMockHandler = [
  // 取得RS queue study列表 - GET /rs/queue/studies
  http.get(`${apiUrl}/rs/queue/studies`, async ({ request }) => {
    const url = new URL(request.url)
    const patientId = url.searchParams.get('patient_id')
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const pageSize = 10

    await delay(300)

    let filteredGroups = mockRsQueueGroups

    if (patientId) {
      filteredGroups = filterRsQueueGroupsByPatientId(patientId)
    }

    // Pagination
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedGroups = filteredGroups.slice(startIndex, endIndex)

    return HttpResponse.json({
      data: paginatedGroups,
    })
  }),

  // 取得RS queue detail - GET /rs/queue/studies/{study_uid}/rs
  http.get(`${apiUrl}/rs/queue/studies/:studyUid/rs`, async ({ params }) => {
    const { studyUid } = params

    await delay(200)

    // Find the group by study_uid to get patient_id
    const group = mockRsQueueGroups.find((g) => g.study_uid === studyUid)
    const items = group ? getRsQueueItemsByPatientId(group.patient_id) : []

    return HttpResponse.json({
      data: items,
    })
  }),

  // 取得RS Structure資料 - GET /rs/queue/rs/{rs_id}/structures
  http.get(`${apiUrl}/rs/queue/rs/:rsId/structures`, async () => {
    await delay(200)

    return HttpResponse.json({
      structures: [
        { id: 1, name: 'Liver', type: 'organ' },
        { id: 2, name: 'Lung', type: 'organ' },
      ],
    })
  }),

  // 刪除RS queue detail - DELETE /rs/queue/rs/{rs_id}
  http.delete(`${apiUrl}/rs/queue/rs/:rsId`, async ({ params }) => {
    const { rsId } = params

    await delay(200)

    return HttpResponse.json({
      success: true,
      message: `RS item ${rsId} deleted successfully`,
    })
  }),

  // Update RS Queue Item Status
  http.patch(`${apiUrl}/rs-queue/items/:id/status`, async ({ params, request }) => {
    const { id } = params
    const { status } = await request.json() as { status: string }

    await delay(300)

    return HttpResponse.json({
      success: true,
      message: `RS Queue item ${id} status updated to ${status}`,
      updated_item: {
        id: parseInt(id as string, 10),
        status,
      },
    })
  }),

  // Delete RS Queue Item
  http.delete(`${apiUrl}/rs-queue/items/:id`, async ({ params }) => {
    const { id } = params

    await delay(300)

    return HttpResponse.json({
      success: true,
      message: `RS Queue item ${id} deleted successfully`,
    })
  }),

  // Process RS Queue Item
  http.post(`${apiUrl}/rs-queue/items/:id/process`, async ({ params }) => {
    const { id } = params

    await delay(500)

    return HttpResponse.json({
      success: true,
      message: `RS Queue item ${id} processing started`,
      processing_id: `proc_${id}_${Date.now()}`,
    })
  }),
]

// RS Templates API
const rsTemplatesMockHandler = [
  // 取得RS template list - GET /rs/templates
  http.get('*/rs/templates', async ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const pageSize = 10

    await delay(200)

    // Pagination
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedTemplates = mockRsTemplates.slice(startIndex, endIndex)

    return HttpResponse.json({
      data: paginatedTemplates,
    })
  }),

  // 取得RS template - GET /rs/templates/{template_id}
  http.get('*/rs/templates/:templateId', async ({ params }) => {
    const { templateId } = params
    const templateIdNum = parseInt(templateId as string, 10)

    await delay(200)

    const template = mockRsTemplates.find((t) => t.id === templateIdNum)

    if (!template) {
      return HttpResponse.json(
        { error: 'Template not found' },
        { status: 404 },
      )
    }

    return HttpResponse.json({
      template,
    })
  }),

  // 新增RS template - POST /rs/templates
  http.post('*/rs/templates', async ({ request }) => {
    const body = await request.json() as { name: string; description?: string }

    await delay(300)

    const newTemplate: RsTemplateItemType = {
      id: mockRsTemplates.length + 1,
      name: body.name,
      description: body.description,
      last_modified: new Date().toISOString(),
    }

    mockRsTemplates.push(newTemplate)

    return HttpResponse.json({
      success: true,
      message: 'Template created successfully',
      template: newTemplate,
    })
  }),

  // 更新RS template - PUT /rs/templates/{template_id}
  http.put('*/rs/templates/:templateId', async ({ params, request }) => {
    const { templateId } = params
    const templateIdNum = parseInt(templateId as string, 10)
    const body = await request.json() as { name: string; description?: string }

    await delay(300)

    const templateIndex = mockRsTemplates.findIndex((t) => t.id === templateIdNum)

    if (templateIndex === -1) {
      return HttpResponse.json(
        { error: 'Template not found' },
        { status: 404 },
      )
    }

    const updatedTemplate = {
      ...mockRsTemplates[templateIndex],
      name: body.name,
      description: body.description,
      last_modified: new Date().toISOString(),
    }

    mockRsTemplates[templateIndex] = updatedTemplate

    return HttpResponse.json({
      success: true,
      message: 'Template updated successfully',
      template: updatedTemplate,
    })
  }),

  // 刪除RS template - DELETE /rs/templates/{template_id}
  http.delete('*/rs/templates/:templateId', async ({ params }) => {
    const { templateId } = params
    const templateIdNum = parseInt(templateId as string, 10)

    await delay(300)

    const templateIndex = mockRsTemplates.findIndex((t) => t.id === templateIdNum)

    if (templateIndex === -1) {
      return HttpResponse.json(
        { error: 'Template not found' },
        { status: 404 },
      )
    }

    mockRsTemplates.splice(templateIndex, 1)

    return HttpResponse.json({
      success: true,
      message: 'Template deleted successfully',
    })
  }),
]

export const handlers = [
  ...rsQueueMockHandler,
  ...rsTemplatesMockHandler,
]
