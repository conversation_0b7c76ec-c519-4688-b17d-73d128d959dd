import { combineReducers } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/es/storage'

// eslint-disable-next-line import/no-cycle
import authReducer from './reducers/authSlice'
import configReducer from './reducers/configSlice'
import detailReducer from './reducers/detailSlice'
import historyReducer from './reducers/historySlice'
import notifyReducer from './reducers/notifySlice'
import protocolReducer from './reducers/protocolSlice'
import remoteReducer from './reducers/remoteSlice'
import rsQueueReducer from './reducers/rsQueueSlice'
import rsTemplatesReducer from './reducers/rsTemplatesSlice'
import structureReducer from './reducers/structureSlice'
import taskListSlice from './reducers/taskListSlice'
import websocketReducer from './reducers/websocketSlice'
import worklistReducer from './reducers/worklistSlice'
import { api } from '../services/api'

const authReducerConfig = {
  key: 'authReducer',
  storage,
  whitelist: ['isAuth', 'rememberAccount', 'token', 'username'],
}

const rootReducer = combineReducers({
  [api.reducerPath]: api.reducer,
  authReducer: persistReducer(authReducerConfig, authReducer),
  configReducer,
  detailReducer,
  historyReducer,
  notifyReducer,
  protocolReducer,
  remoteReducer,
  rsQueueReducer,
  rsTemplatesReducer,
  structureReducer,
  taskListSlice,
  websocketReducer,
  worklistReducer,
})

export default rootReducer
