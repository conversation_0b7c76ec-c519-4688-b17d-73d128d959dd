// eslint-disable-next-line import/no-extraneous-dependencies
import { arrayMove } from '@dnd-kit/sortable'
import { createSlice } from '@reduxjs/toolkit'

import { api } from 'src/services/api'

export interface Protocols {
  protocols: ProtocolType[]
  protocolSearchModeCheck: boolean
  updateProtocolSortCheck: boolean
}

const initialState: Protocols = {
  protocols: [],
  protocolSearchModeCheck: false,
  updateProtocolSortCheck: false,
}

export const protocolSlice = createSlice({
  name: 'protocol',
  initialState,
  reducers: {
    resetProtocolState: () => initialState,
    clearProtocol: (state) => {
      state.protocols = initialState.protocols
      state.protocolSearchModeCheck = initialState.protocolSearchModeCheck
      state.updateProtocolSortCheck = initialState.updateProtocolSortCheck
    },
    updateProtocolSearchMode: (state, { payload }) => {
      const { bool } = payload
      state.protocolSearchModeCheck = bool
    },
    updateProtocolSort: (state, { payload }) => {
      const { activeId, overId } = payload
      const activeIndex = state.protocols.findIndex((i) => i.id === activeId)
      const overIndex = state.protocols.findIndex((i) => i.id === overId)

      state.protocols = arrayMove(state.protocols, activeIndex, overIndex)
      state.updateProtocolSortCheck = true
    },
    updateProtocols: (state, { payload }) => {
      state.protocols = payload
    },
    deleteProtocol: (state, { payload }) => {
      state.protocols = state.protocols.filter((item) => item.id !== payload)
    },
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getProtocol.matchFulfilled,
        (state, { payload }) => {
          state.protocols = payload.protocols
          state.updateProtocolSortCheck = initialState.updateProtocolSortCheck
        },
      )
  },
})

export const {
  resetProtocolState,
  clearProtocol,
  updateProtocols,
  updateProtocolSearchMode,
  updateProtocolSort,
  deleteProtocol,
} = protocolSlice.actions
export default protocolSlice.reducer
