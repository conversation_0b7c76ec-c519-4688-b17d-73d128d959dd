import { createSlice } from '@reduxjs/toolkit'

import { color } from 'src/utils/variables'

// eslint-disable-next-line import/no-cycle
import { api } from '../../services/api'

export interface DetailState {
  detail: WorklistDetailType | ProtocolDetailType | ProcessStructuresDetailType
  structureSort: { id: number, sort: number | null, customized: boolean }[]
  isDetailUpdate: boolean
}

const initialCustomizedStructure: CustomizedStructuresType = {
  id: 0,
  show: true,
  name: undefined,
  color_code: color.primary.picker,
  type: undefined,
  sort: null,
  mode: '',
  margin_value: null,
  structure_operations: [],
  margin_symmetric: true,
}

const initialState: DetailState = {
  detail: {
    protocol_name: '',
    description: '',
    patient_id: '',
    structure_set_label: '',
    rs_set_label: '',
    status: 'ACTIVE',
    study_date: '',
    study_description: '',
    use_protocol: null,
    use_template: null,
    protocol_description: '',
    template_description: '',
    series_time: '',
    series_description: '',
    source: {
      remote_server: [],
      folder: [],
    },
    destination: {
      remote_server: [],
      folder: [],
    },
    study_info: [],
    structures: [],
    customized_structures: [],
  } as any,
  structureSort: [],
  isDetailUpdate: false,
}

export const detailSlice = createSlice({
  name: 'worklist',
  initialState,
  reducers: {
    resetDetailState: () => initialState,
    clearDetail: (state) => {
      state.detail = initialState.detail
      state.isDetailUpdate = initialState.isDetailUpdate
    },
    updateInput: (state, { payload }) => {
      const { label, value } = payload;
      (state.detail as any)[label] = value
      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
      if ('use_template' in state.detail) {
        state.detail.use_template = null
      }
    },
    updateDetail: (state, { payload }) => {
      state.detail = { ...state.detail, ...payload }
      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    updateProtocol: (state, { payload }) => {
      const { value } = payload
      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = value
      }
    },
    updateRemote: (state, { payload }) => {
      const { type, remote, value } = payload
      const remoteName = remote === 'remote_server' ? 'remote_server' : 'folder'
      if ('source' in state.detail) {
        state.detail[type as SourceDestinationKey][remoteName] = value
      } else {
        state.detail.destination[remoteName] = value
      }

      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    updateStructure: (state, { payload }) => {
      const { id } = payload
      const { structures } = state.detail
      const structureIndex = structures.findIndex((item) => item.id === id)

      if (structureIndex !== -1) {
        structures.splice(structureIndex, 1)
      } else {
        structures.push({ ...payload, id, sort: null })
      }

      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    addCustomizedStructures: (state) => {
      const { customized_structures: customizedStructures } = state.detail
      customizedStructures.push({
        ...initialCustomizedStructure,
        id: Date.now(),
      })

      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    updateCustomizedStructures: (state, { payload }) => {
      const { data } = payload
      const { customized_structures: customizedStructures } = state.detail
      state.detail.customized_structures = customizedStructures.map(
        (item) => (item.id === data.id ? { ...item, ...data } : item),
      )
      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    deleteCustomizedStructures: (state, { payload }) => {
      const { data } = payload
      const { customized_structures: customizedStructures } = state.detail
      state.detail.customized_structures = customizedStructures.filter((item) => item.id !== data.id)
      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    updateStructureSort: (state, { payload }) => {
      state.structureSort = payload
      state.isDetailUpdate = true
      if ('use_protocol' in state.detail) {
        state.detail.use_protocol = null
      }
    },
    addStudyInfo: (state, { payload }) => {
      const { data } = payload
      if (!('study_info' in state.detail)) return
      const { study_info: studyInfo } = state.detail
      const maxId = Math.max(...studyInfo.map((item) => item.id).filter((id): id is number => id !== undefined))
      studyInfo.push({ ...data, id: maxId === -Infinity ? 0 : maxId + 1 })
      state.isDetailUpdate = true
    },
    updateStudyInfo: (state, { payload }) => {
      const { data } = payload
      if (!('study_info' in state.detail)) return
      const { study_info: studyInfo } = state.detail
      state.detail.study_info = studyInfo.map((item) => (item.id === data.id
        ? { ...item, ...data }
        : item))
      state.isDetailUpdate = true
    },
    deleteStudyInfo: (state, { payload }) => {
      const { data } = payload
      if (!('study_info' in state.detail)) return
      const { study_info: studyInfo } = state.detail
      state.detail.study_info = studyInfo.filter((item) => item.id !== data.id)
      state.isDetailUpdate = true
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getWorklistDetail.matchFulfilled,
        (state, { payload }) => {
          state.structureSort = [
            ...payload.structures
              .map(({ id, sort }) => ({ id, sort, customized: false })),
            ...payload.customized_structures
              .map(({ id, sort }) => ({ id, sort, customized: true })),
          ]
          state.detail = payload
          state.isDetailUpdate = initialState.isDetailUpdate
        },
      )
      .addMatcher(
        api.endpoints.getProtocolDetail.matchFulfilled,
        (state, { payload }) => {
          state.structureSort = [
            ...payload.structures
              .map(({ id, sort }) => ({ id, sort, customized: false })),
            ...payload.customized_structures
              .map(({ id, sort }) => ({ id, sort, customized: true })),
          ]
          state.detail = {
            ...payload,
            study_info: payload.study_info.map((item, index) => ({ ...item, id: index })),
          }
          state.isDetailUpdate = initialState.isDetailUpdate
        },
      )
      .addMatcher(
        api.endpoints.getWorklistProtocolDetail.matchFulfilled,
        (state, { payload }) => {
          Object.assign(state.detail, {
            structure_set_label: payload.structure_set_label,
            destination: payload.destination,
            structures: payload.structures,
            customized_structures: payload.customized_structures,
          })

          if ('protocol_description' in state.detail) {
            Object.assign(state.detail, {
              protocol_description: payload.description,
              use_protocol: payload.protocol_name,
            })
          }
        },
      )
      .addMatcher(
        api.endpoints.getProcessStructuresDetail.matchFulfilled,
        (state, { payload }) => {
          state.structureSort = [
            ...payload.structures
              .map(({ id, sort }) => ({ id, sort, customized: false })),
            ...payload.customized_structures
              .map(({ id, sort }) => ({ id, sort, customized: true })),
          ]
          state.detail = payload
          state.isDetailUpdate = initialState.isDetailUpdate
        },
      )
  },
})

export const {
  resetDetailState,
  clearDetail,
  updateDetail,
  updateInput,
  updateProtocol,
  updateRemote,
  updateStructure,
  addCustomizedStructures,
  updateCustomizedStructures,
  deleteCustomizedStructures,
  updateStructureSort,
  addStudyInfo,
  updateStudyInfo,
  deleteStudyInfo,
} = detailSlice.actions

export default detailSlice.reducer
