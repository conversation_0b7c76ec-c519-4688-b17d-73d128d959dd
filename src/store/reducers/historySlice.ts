import { createSlice } from '@reduxjs/toolkit'

import { api } from '../../services/api'

interface HistoryState {
  historyFocus: string | null
  searchParameter: {
    patientId?: string
    studyStatus?: string
    studyDateRangeStart?: string
    studyDateRangeEnd?: string
  }
  sorter: {
    orderKey?: string
    ascend: boolean
  }
  pagination: {
    current: number
    total: number
    pageSize: number
  }
}

const initialState: HistoryState = {
  historyFocus: null,
  searchParameter: {
    patientId: undefined,
    studyStatus: undefined,
    studyDateRangeStart: undefined,
    studyDateRangeEnd: undefined,
  },
  sorter: {
    orderKey: '',
    ascend: true,
  },
  pagination: {
    current: 1,
    total: 0,
    pageSize: 0,
  },
}

const historyTableSlice = createSlice({
  name: 'historyTable',
  initialState,
  reducers: {
    setHistoryFocus(state, { payload }) {
      state.historyFocus = payload
    },
    setSearchParameter(state, { payload }) {
      state.searchParameter = payload
    },
    setSorter(state, { payload }) {
      state.sorter = payload
    },
    setCurrentPage(state, { payload }) {
      state.pagination.current = payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getHistory.matchFulfilled,
        (state, { payload }) => {
          state.pagination = {
            current: payload.current_page,
            total: payload.total_data_count,
            pageSize: payload.page_size,
          }
        },
      )
  },
})

export const {
  setHistoryFocus,
  setSearchParameter,
  setSorter,
  setCurrentPage,
} = historyTableSlice.actions
export default historyTableSlice.reducer
