import { createSlice } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from 'src/services/api'

export type ConfigRequiredKey = 'name'
| 'patient_id'
| 'structure_set_label'
| 'source'
| 'destination'
| 'structures'
| 'study_info'
| 'rs_set_label'

export interface ConfigRequiredType extends Record<ConfigRequiredKey, boolean> {
  customized_structures: CustomizedStructuresType[]
}

export interface Config {
  remoteServerConfig: RemoteConfigType[]
  folderConfig: RemoteConfigType[]
  structureConfig: StructureConfigType[]
  protocolConfig: ProtocolConfigType[]
  categoryType: string[]
  customizedStructureType: string[]
  configRequired: ConfigRequiredType,
}

const initialState: Config = {
  remoteServerConfig: [],
  folderConfig: [],
  structureConfig: [],
  protocolConfig: [],
  customizedStructureType: [],
  categoryType: [],
  configRequired: {
    name: false,
    patient_id: false,
    structure_set_label: false,
    source: false,
    destination: false,
    structures: false,
    study_info: false,
    customized_structures: [],
    rs_set_label: false,
  },
}

export const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {
    resetConfigState: () => initialState,
    resetConfigRequired: (state) => {
      state.configRequired = initialState.configRequired
    },
    updateConfigRequired: (state, { payload }) => {
      const { rules } = payload
      state.configRequired = { ...state.configRequired, ...rules }
    },
    cleanCustomizedStructuresRequired: (state, { payload }) => {
      state.configRequired.customized_structures = state.configRequired.customized_structures
        .filter((item) => item.id !== payload.id)
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getStructureConfig.matchFulfilled,
        (state, { payload }) => {
          state.structureConfig = payload.structures
          state.categoryType = payload.category_type
          state.customizedStructureType = payload.customized_structures_type
          state.configRequired = initialState.configRequired
        },
      )
      .addMatcher(
        api.endpoints.getRemoteConfig.matchFulfilled,
        (state, { payload }) => {
          state.remoteServerConfig = payload.remote_server_list
          state.folderConfig = payload.folder_list
          state.configRequired = initialState.configRequired
        },
      )
      .addMatcher(
        api.endpoints.getProtocolConfig.matchFulfilled,
        (state, { payload }) => {
          state.protocolConfig = payload.protocols
          state.configRequired = initialState.configRequired
        },
      )
  },
})

export const {
  resetConfigState,
  updateConfigRequired,
  resetConfigRequired,
  cleanCustomizedStructuresRequired,
} = configSlice.actions
export default configSlice.reducer
