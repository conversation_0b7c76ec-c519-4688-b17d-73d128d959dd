import { createSlice } from '@reduxjs/toolkit'

import { api } from '../../services/api'

interface TaskListState {
  taskListFocus: number | null
  searchParameter: {
    patientId?: string
    studyStatus?: string
  }
  sorter: {
    orderKey?: string
    ascend: boolean
  }
  pagination: {
    current: number
    total: number
    pageSize: number
  }
}

const initialState: TaskListState = {
  taskListFocus: null,
  searchParameter: {
    patientId: undefined,
    studyStatus: undefined,
  },
  sorter: {
    orderKey: '',
    ascend: true,
  },
  pagination: {
    current: 1,
    total: 0,
    pageSize: 0,
  },
}

const taskTableSlice = createSlice({
  name: 'taskListTable',
  initialState,
  reducers: {
    setTaskListFocus(state, { payload }) {
      state.taskListFocus = payload
    },
    setSearchParameter(state, { payload }) {
      state.searchParameter = payload
    },
    setSorter(state, { payload }) {
      state.sorter = payload
    },
    setCurrentPage(state, { payload }) {
      state.pagination.current = payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getWorklistGroup.matchFulfilled,
        (state, { payload }) => {
          state.pagination = {
            current: payload.current_page,
            total: payload.total_data_count,
            pageSize: payload.page_size,
          }
        },
      )
  },
})

export const {
  setTaskListFocus,
  setSearchParameter,
  setSorter,
  setCurrentPage,
} = taskTableSlice.actions
export default taskTableSlice.reducer
