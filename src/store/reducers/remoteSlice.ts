import { createSlice } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from '../../services/api'

export interface Remote {
  remoteServer: RemoteServerType[]
  folder: FolderType[]
  updateRemoteServerIds: number[]
  updateFolderIds: number[]
}

const initialState: Remote = {
  remoteServer: [],
  folder: [],
  updateRemoteServerIds: [],
  updateFolderIds: [],
}

export const remoteSlice = createSlice({
  name: 'remote',
  initialState,
  reducers: {
    resetRemoteState: () => initialState,
    updateRemote: (state, { payload }) => {
      const { remote, data } = payload
      const { id } = data
      if (remote === 'remote server') {
        state.remoteServer = state.remoteServer.map((item) => (item.id === id ? { ...item, ...data } : item))
        if (!state.updateRemoteServerIds.includes(id)) {
          state.updateRemoteServerIds.push(id)
        }
      } else if (remote === 'folder') {
        state.folder = state.folder.map((item) => (item.id === id ? { ...item, ...data } : item))
        if (!state.updateFolderIds.includes(id)) {
          state.updateFolderIds.push(id)
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getRemote.matchFulfilled,
        (state, { payload }) => {
          state.remoteServer = payload.remote_server_list
          state.folder = payload.folder_list
          state.updateRemoteServerIds = initialState.updateRemoteServerIds
          state.updateFolderIds = initialState.updateFolderIds
        },
      )
      .addMatcher(
        api.endpoints.getRemoteStatus.matchFulfilled,
        (state, { payload }) => {
          state.remoteServer = state.remoteServer.map((remoteServer) => {
            const updatedServer = payload.remote_server_list.find((item) => item.id === remoteServer.id)
            return updatedServer ? { ...remoteServer, connect_status: updatedServer.connect_status } : remoteServer
          })
          state.folder = state.folder.map((folder) => {
            const updatedFolder = payload.folder_list.find((item) => item.id === folder.id)
            return updatedFolder ? { ...folder, connect_status: updatedFolder.connect_status } : folder
          })
        },
      )
  },
})

export const {
  resetRemoteState,
  updateRemote,
} = remoteSlice.actions
export default remoteSlice.reducer
