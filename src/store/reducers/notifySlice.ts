import { createSlice } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from '../../services/api'

export interface Notify {
  totalNotify: number
  notifyQuantity: number
  notifyList: NotifyType[]
}

const initialState: Notify = {
  totalNotify: 0,
  notifyQuantity: 10,
  notifyList: [],
}

export const notifySlice = createSlice({
  name: 'notify',
  initialState,
  reducers: {
    resetNotifyState: () => initialState,
    clearNotifyQuantity: (state) => {
      state.notifyQuantity = initialState.notifyQuantity
      state.notifyList = state.notifyList.slice(0, 10)
    },
    updateNotifyQuantity: (state, { payload }) => {
      const { quantity } = payload
      state.notifyQuantity = quantity
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getNotify.matchFulfilled,
        (state, { payload }) => {
          state.totalNotify = payload.total_notify
          state.notifyList = payload.notify_list
        },
      )
      .addMatcher(
        api.endpoints.reGetNotify.matchFulfilled,
        (state, { payload }) => {
          state.totalNotify = payload.total_notify
          state.notifyList = payload.notify_list
        },
      )
  },
})

export const { resetNotifyState, clearNotifyQuantity, updateNotifyQuantity } = notifySlice.actions
export default notifySlice.reducer
