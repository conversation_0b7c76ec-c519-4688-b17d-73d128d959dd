import { createSlice } from '@reduxjs/toolkit'

import { api } from '../../services/api'

export interface Auth {
  isAuth: boolean
  rememberAccount: string | undefined
  token: string | undefined
  username: string | undefined
}

const initialState: Auth = {
  isAuth: false,
  rememberAccount: undefined,
  token: undefined,
  username: undefined,
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    resetAuthState: () => initialState,
    kickOff: (state) => {
      if (state.isAuth) {
        state.isAuth = initialState.isAuth
        state.token = initialState.token
        state.username = initialState.username
      }
    },
    refreshToken: (state, { payload }) => {
      state.token = payload.token
    },
    updateRememberAccount: (state, { payload }) => {
      state.rememberAccount = payload.account
    },
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.postLogin.matchFulfilled,
        (state, { payload }) => {
          state.isAuth = true
          state.token = payload.token
          state.username = payload.user_name
        },
      )
      .addMatcher(
        api.endpoints.deleteLogin.matchFulfilled,
        (state) => {
          state.isAuth = initialState.isAuth
          state.token = initialState.token
          state.username = initialState.username
        },
      )
  },
})

export const {
  resetAuthState,
  kickOff,
  refreshToken,
  updateRememberAccount,
} = authSlice.actions
export default authSlice.reducer
