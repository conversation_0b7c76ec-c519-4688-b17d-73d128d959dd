import { createSlice } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from '../../services/api'

export interface Structure {
  structures: StructureType[]
  updateStructureIds: number[]
}

const initialState: Structure = {
  structures: [],
  updateStructureIds: [],
}

export const structureSlice = createSlice({
  name: 'structure',
  initialState,
  reducers: {
    resetStructureState: () => initialState,
    updateStructure: (state, { payload }) => {
      const { id } = payload
      state.structures = state.structures.map((item) => (item.id === id ? { ...item, ...payload } : item))
      if (!state.updateStructureIds.includes(id)) {
        state.updateStructureIds.push(id)
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getStructure.matchFulfilled,
        (state, { payload }) => {
          state.structures = payload.structures
          state.updateStructureIds = initialState.updateStructureIds
        },
      )
  },
})

export const {
  resetStructureState,
  updateStructure,
} = structureSlice.actions
export default structureSlice.reducer
