import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query/react'
import {
  FLUSH,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
  REHYDRATE,
} from 'redux-persist'

// eslint-disable-next-line import/no-cycle
import rootReducer from './rootReducer'
import { api } from '../services/api'

export const setupStore = (preloadedState?: Partial<RootState>) => configureStore({
  reducer: rootReducer,
  preloadedState,
  middleware: (getDefaultModdleware) => getDefaultModdleware({
    serializableCheck: {
      ignoredActions: [FLUSH, REHYDRATE, PAUSE, <PERSON>ERSIST, PURGE, REGISTER],
    },
  }).concat(api.middleware),
})

export const store = setupStore()

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof rootReducer>
export type AppStore = ReturnType<typeof setupStore>
export type AppDispatch = typeof store.dispatch
export default store
