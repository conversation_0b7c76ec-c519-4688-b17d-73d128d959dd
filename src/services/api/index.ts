import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

import type { RootState } from '@/store/index'

// eslint-disable-next-line import/no-cycle
import { baseQueryWithReauth } from './base/base-query'
import { createAuthEndpoints } from './modules/auth'
import { createDownloadEndpoints } from './modules/download'
import { createHistoryEndpoints } from './modules/history'
import { createNotifyEndpoints } from './modules/notify'
import { createProcessStructuresEndpoints } from './modules/processStructures'
import { createProtocolEndpoints } from './modules/protocol'
import { createRemoteEndpoints } from './modules/remote'
import { createRsQueueEndpoints } from './modules/rsQueue'
import { createRsTemplatesEndpoints } from './modules/rsTemplates'
import { createStructureEndpoints } from './modules/structure'
import { createWorklistEndpoints } from './modules/worklist'

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth(fetchBaseQuery({
    baseUrl: window.config.env.api.rest,
    prepareHeaders: (headers, { getState }) => {
      const { token } = (getState() as RootState).authReducer

      if (token) {
        headers.set('authorization', `Bearer ${token}`)
      }

      // Security headers
      headers.set('X-Frame-Options', 'DENY')
      headers.set('Content-Security-Policy', 'frame-ancestors \'none\'')

      return headers
    },
  })),
  endpoints: (builder) => ({
    ...createAuthEndpoints(builder),
    ...createNotifyEndpoints(builder),
    ...createWorklistEndpoints(builder),
    ...createProtocolEndpoints(builder),
    ...createStructureEndpoints(builder),
    ...createProcessStructuresEndpoints(builder),
    ...createRemoteEndpoints(builder),
    ...createRsTemplatesEndpoints(builder),
    ...createRsQueueEndpoints(builder),
    ...createDownloadEndpoints(builder),
    ...createHistoryEndpoints(builder),
  }),
})

// Export hooks for components
export const {
  // Auth
  usePostLoginMutation,
  useDeleteLoginMutation,

  // Notify
  useGetNotifyMutation,
  useReGetNotifyMutation,
  useDeleteNotifyMutation,

  // History
  useGetHistoryQuery,
  useGetPatientHistoryQuery,

  // Worklist
  useGetWorklistGroupQuery,
  useReGetWorklistGroupQuery,
  usePatchPrioritizeWorklistGroupMutation,
  usePatchWorklistGroupMutation,
  useGetWorklistQuery,
  useReGetWorklistQuery,
  usePatchWorklistStatusMutation,
  useGetWorklistDetailMutation,
  usePutWorklistMutation,
  usePostWorklistRedrawMutation,
  usePostRsResendMutation,
  useGetRsPreviewZipMutation,

  // Protocol
  useLazyGetProtocolQuery,
  useGetProtocolConfigMutation,
  useGetProtocolDetailMutation,
  useGetWorklistProtocolDetailMutation,
  useCheckProtocolNameMutation,
  usePostProtocolMutation,
  usePutProtocolMutation,
  useDeleteProtocolMutation,
  usePutProtocolSortMutation,
  usePatchProtocolStatusMutation,

  // Structure
  useGetStructureMutation,
  usePutStructureMutation,
  useGetStructureConfigMutation,

  // Process Structures
  useGetProcessStructuresDetailMutation,
  usePostProcessStructuresMutation,
  usePutProcessStructuresMutation,

  // Remote
  useGetRemoteMutation,
  useGetRemoteStatusMutation,
  useGetRemoteConfigMutation,
  usePutRemoteMutation,
  usePostRemoteServerMutation,
  useDeleteRemoteServerMutation,
  usePostFolderMutation,
  useDeleteFolderMutation,

  // RS Queue
  useGetRsQueueStudiesQuery,
  useLazyGetRsQueueStudiesQuery,
  useGetRsQueueDetailQuery,
  useLazyGetRsQueueDetailQuery,
  useGetRsStructureQuery,
  useLazyGetRsStructureQuery,
  useDeleteRsQueueItemMutation,

  // RS Templates
  useGetRsTemplatesQuery,
  useLazyGetRsTemplatesQuery,
  useGetRsTemplateQuery,
  useLazyGetRsTemplateQuery,
  useCreateRsTemplateMutation,
  useUpdateRsTemplateMutation,
  useDeleteRsTemplateMutation,

  // Download
  useGetDicomMutation,
} = api
