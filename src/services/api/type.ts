export interface BaseRes {
  result: string
}

export interface ApiErrorRes {
  status: number
  data: {
    detail?: string
    message?: string
  }
}

export interface PaginationMeta {
  current_page: number
  page_size: number
  total_data_count: number
}

export interface SortParams {
  order_key?: string
  ascend?: boolean
}

export interface StudyDateRangeFilter {
  study_date_range_start?: string
  study_date_range_end?: string
}

export interface ResponseData<T> {
  data: T
}
