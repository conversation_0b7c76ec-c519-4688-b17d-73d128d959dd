import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { extractFilename, triggerDownload } from '../base/helpers'

interface GetDicomReq {
  id: number
  rs?: boolean
  ct?: boolean
}

export function createDownloadEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Download DICOM files
    getDicom: builder.mutation<void, GetDicomReq>({
      query: ({ id, rs, ct }: GetDicomReq) => ({
        url: `/task/${id}/download`,
        method: 'GET',
        params: { rs, ct },
        responseHandler: async (response: Response) => {
          if (response.status !== 200) {
            return response.json()
          }

          const blob = await response.blob()
          const disposition = response.headers.get('content-disposition')
          const filename = extractFilename(disposition)

          triggerDownload(blob, filename)
          return undefined
        },
        cache: 'no-cache',
      }),
    }),
  }
}
