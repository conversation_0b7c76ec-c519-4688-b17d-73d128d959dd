import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes, ResponseData } from '../type'
import { UpdateRsTemplateReq } from './rs.type'

interface RsTemplateDetailReq {
  template_id: number
}

export function createRsTemplatesEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // 取得 RS template list
    getRsTemplates: builder.query<ResponseData<RsTemplateItemType[]>, void>({
      query: () => createApiQuery({
        url: '/rs/templates',
        method: 'GET',
      }),
    }),

    // 取得 RS template
    getRsTemplate: builder.query<RsTemplateItemDetailType, RsTemplateDetailReq>({
      query: ({ template_id }) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'GET',
      }),
    }),

    // 新增 RS template
    createRsTemplate: builder.mutation<BaseRes, RsTemplateItemDetailType>({
      query: (params: RsTemplateItemDetailType) => createApiQuery({
        url: '/rs/templates',
        method: 'POST',
        body: params,
      }),
    }),

    // 更新 RS template
    updateRsTemplate: builder.mutation<BaseRes, UpdateRsTemplateReq>({
      query: ({ template_id, ...body }: UpdateRsTemplateReq) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'PUT',
        body,
      }),
    }),

    // 刪除 RS template
    deleteRsTemplate: builder.mutation<BaseRes, RsTemplateDetailReq>({
      query: ({ template_id }) => createApiQuery({
        url: `/rs/templates/${template_id}`,
        method: 'DELETE',
      }),
    }),
  }
}
