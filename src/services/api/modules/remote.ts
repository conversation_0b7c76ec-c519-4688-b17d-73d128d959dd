import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'
import {
  GetRemoteReq,
  PostRemoteServerReq,
  PostFolderReq,
  PutRemoteReq,
  DeleteRemoteServerReq,
  DeleteFolderReq,
  GetRemoteRes,
  GetRemoteConfigRes,
} from './remote.type'

export function createRemoteEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get remote servers and folders
    getRemote: builder.mutation<GetRemoteRes, GetRemoteReq>({
      query: (params: GetRemoteReq) => createApiQuery({
        url: '/source_destination',
        method: 'GET',
        params,
      }),
    }),

    // Get remote status (for polling without refresh token)
    getRemoteStatus: builder.mutation<GetRemoteRes, GetRemoteReq>({
      query: (params: GetRemoteReq) => createApiQuery({
        url: '/source_destination',
        method: 'GET',
        params,
      }),
    }),

    // Get remote configuration
    getRemoteConfig: builder.mutation<GetRemoteConfigRes, void>({
      query: () => createApiQuery({
        url: '/source_destination',
        method: 'GET',
        params: { config_mode: true },
      }),
    }),

    // Update remote configuration
    putRemote: builder.mutation<BaseRes, PutRemoteReq>({
      query: ({ type, remote_server_list, folder_list }: PutRemoteReq) => createApiQuery({
        url: '/source_destination',
        method: 'PUT',
        params: { type },
        body: { remote_server_list, folder_list },
      }),
    }),

    // Add remote server
    postRemoteServer: builder.mutation<BaseRes, PostRemoteServerReq>({
      query: ({ type, ...data }: PostRemoteServerReq) => {
        return createApiQuery({
          url: `/source_destination/${type}/remote_server`,
          method: 'POST',
          body: data,
        })
      },
    }),

    // Delete remote server
    deleteRemoteServer: builder.mutation<BaseRes, DeleteRemoteServerReq>({
      query: ({ type, ...data }: DeleteRemoteServerReq) => createApiQuery({
        url: `/source_destination/${type}/remote_server`,
        method: 'DELETE',
        body: data,
      }),
    }),

    // Add folder
    postFolder: builder.mutation<BaseRes, PostFolderReq>({
      query: ({ type, ...data }: PostFolderReq) => createApiQuery({
        url: `/source_destination/${type}/folder`,
        method: 'POST',
        body: data,
      }),
    }),

    // Delete folder
    deleteFolder: builder.mutation<BaseRes, DeleteFolderReq>({
      query: ({ type, ...data }: DeleteFolderReq) => createApiQuery({
        url: `/source_destination/${type}/folder`,
        method: 'DELETE',
        body: data,
      }),
    }),
  }
}
