import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { buildApiUrl } from '../base/helpers'

export function createHistoryEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // History
    getHistory: builder.query<any, Record<string, any>>({
      query: (params) => buildApiUrl({ url: 'history', params }),
    }),

    // Patient history
    getPatientHistory: builder.query<any, { patient_id: string, [key: string]: any }>({
      query: ({ patient_id, ...params }) => buildApiUrl({ url: `history/patient/${patient_id}`, params }),
    }),
  }
}
