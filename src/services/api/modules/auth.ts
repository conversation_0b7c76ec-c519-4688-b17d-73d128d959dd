import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'

interface LoginReq {
  account: string
  password: string
}

interface LoginRes {
  user_name: string
  token: string
}

export function createAuthEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // User login
    postLogin: builder.mutation<LoginRes, LoginReq>({
      query: (credentials: LoginReq) => createApiQuery({
        url: '/login',
        method: 'POST',
        body: credentials,
      }),
    }),

    // User logout
    deleteLogin: builder.mutation<BaseRes, void>({
      query: () => createApiQuery({
        url: '/login',
        method: 'DELETE',
      }),
    }),
  }
}
