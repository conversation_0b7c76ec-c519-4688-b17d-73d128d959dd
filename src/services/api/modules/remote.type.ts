// ------------------------ request ------------------------
export interface GetRemoteReq {
  type?: string
}

export interface PostRemoteServerReq {
  type: string
  name: string
  description: string
  ae_title: string
  ip: string
  port: string
}

export interface PostFolderReq {
  type: string
  folder_name: string
  folder_description: string
  folder_path: string
  folder_account?: string
  folder_password?: string
}

export interface PutRemoteReq {
  type: string
  remote_server_list: RemoteServerType[]
  folder_list: FolderType[]
}

export interface DeleteRemoteServerReq {
  type: string
  ae_title: string
  ip: string
  port: string
}

export interface DeleteFolderReq {
  type: string
  folder_name: string
  folder_path: string
}

// ------------------------ response -----------------------

export interface GetRemoteRes {
  remote_server_list: RemoteServerType[]
  folder_list: FolderType[]
}

export interface GetRemoteConfigRes {
  remote_server_list: RemoteConfigType[]
  folder_list: RemoteConfigType[]
}
