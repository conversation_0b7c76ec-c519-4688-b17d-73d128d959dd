import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'
import type {
  GetProtocolReq,
  GetProtocolDetailReq,
  CheckProtocolNameReq,
  PostProtocolReq,
  PutProtocolReq,
  DeleteProtocolReq,
  PutProtocolSortReq,
  PatchProtocolStatusReq,
  GetProtocolRes,
  GetProtocolDetailRes,
  GetProtocolConfigRes,
} from './protocol.type'

export function createProtocolEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get protocols (query for caching)
    getProtocol: builder.query<GetProtocolRes, GetProtocolReq>({
      query: (params: GetProtocolReq) => createApiQuery({
        url: '/protocol',
        method: 'GET',
        params,
      }),
    }),

    // Get protocol configuration
    getProtocolConfig: builder.mutation<GetProtocolConfigRes, void>({
      query: () => createApiQuery({
        url: '/protocol',
        method: 'GET',
        params: { config_mode: true },
      }),
    }),

    // Get protocol detail
    getProtocolDetail: builder.mutation<GetProtocolDetailRes, GetProtocolDetailReq>({
      query: ({ id }: GetProtocolDetailReq) => createApiQuery({
        url: `/protocol/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Get worklist protocol detail (for update use protocol)
    getWorklistProtocolDetail: builder.mutation<GetProtocolDetailRes, GetProtocolDetailReq>({
      query: ({ id }: GetProtocolDetailReq) => createApiQuery({
        url: `/protocol/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Check protocol name availability
    checkProtocolName: builder.mutation<BaseRes, CheckProtocolNameReq>({
      query: ({ name }: CheckProtocolNameReq) => createApiQuery({
        url: '/protocol/check',
        method: 'POST',
        body: { name },
      }),
    }),

    // Create new protocol
    postProtocol: builder.mutation<BaseRes, PostProtocolReq>({
      query: (data: PostProtocolReq) => createApiQuery({
        url: '/protocol',
        method: 'POST',
        body: data,
      }),
    }),

    // Update protocol
    putProtocol: builder.mutation<BaseRes, PutProtocolReq>({
      query: ({ id, ...data }: PutProtocolReq) => createApiQuery({
        url: `/protocol/${id}`,
        method: 'PUT',
        body: data,
      }),
    }),

    // Delete protocol
    deleteProtocol: builder.mutation<BaseRes, DeleteProtocolReq>({
      query: ({ id }: DeleteProtocolReq) => createApiQuery({
        url: `/protocol/${id}`,
        method: 'DELETE',
      }),
    }),

    // Update protocol sort order
    putProtocolSort: builder.mutation<BaseRes, PutProtocolSortReq>({
      query: ({ protocol_sort }: PutProtocolSortReq) => {
        return createApiQuery({
          url: '/protocol/sort',
          method: 'PUT',
          body: { protocol_sort },
        })
      },
    }),

    // Update protocol status
    patchProtocolStatus: builder.mutation<BaseRes, PatchProtocolStatusReq>({
      query: ({ id, status }: PatchProtocolStatusReq) => {
        return createApiQuery({
          url: `/protocol/${id}/status`,
          method: 'PATCH',
          body: { status },
        })
      },
      // Optimistic update to immediately reflect status change in UI
      async onQueryStarted({ id, status }, { dispatch, getState }) {
        // Get current protocols from store
        const state = getState() as any
        const { protocols } = state.protocolReducer

        // Update the protocol status optimistically
        const updatedProtocols = protocols.map((item: ProtocolType) => {
          return item.id === id ? { ...item, status } : item
        })

        dispatch({
          type: 'protocol/updateProtocols',
          payload: updatedProtocols,
        })
      },
    }),
  }
}
