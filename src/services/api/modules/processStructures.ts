import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'

interface GetProcessStructuresDetailReq {
  id: number
}

interface GetProcessStructuresDetailRes extends ProcessStructuresDetailType {}

interface PostProcessStructuresReq extends ProcessStructuresDetailType {}

interface PutProcessStructuresReq extends ProcessStructuresDetailType {
  id: number
}

export function createProcessStructuresEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get process structures detail
    getProcessStructuresDetail: builder.mutation<GetProcessStructuresDetailRes, GetProcessStructuresDetailReq>({
      query: ({ id }: GetProcessStructuresDetailReq) => createApiQuery({
        url: `/process-structures/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Create new process structures
    postProcessStructures: builder.mutation<BaseRes, PostProcessStructuresReq>({
      query: (data: PostProcessStructuresReq) => createApiQuery({
        url: '/process-structures',
        method: 'POST',
        body: data,
      }),
    }),

    // Update process structures
    putProcessStructures: builder.mutation<BaseRes, PutProcessStructuresReq>({
      query: ({ id, ...data }: PutProcessStructuresReq) => createApiQuery({
        url: `/process-structures/${id}`,
        method: 'PUT',
        body: data,
      }),
    }),
  }
}
