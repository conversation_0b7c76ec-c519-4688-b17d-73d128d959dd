import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'

interface GetNotifyReq {
  quantity: number
}

interface GetNotifyRes {
  total_notify: number
  notify_list: NotifyType[]
}

interface DeleteNotifyReq {
  notify_id?: number
}

export function createNotifyEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get notifications with token refresh
    getNotify: builder.mutation<GetNotifyRes, GetNotifyReq>({
      query: (params: GetNotifyReq) => createApiQuery({
        url: '/notify',
        method: 'GET',
        params,
      }),
    }),

    // Get notifications without token refresh
    reGetNotify: builder.mutation<GetNotifyRes, GetNotifyReq>({
      query: (params: GetNotifyReq) => createApiQuery({
        url: '/notify',
        method: 'GET',
        params,
      }),
    }),

    // Delete notification
    deleteNotify: builder.mutation<BaseRes, DeleteNotifyReq>({
      query: (params: DeleteNotifyReq) => createApiQuery({
        url: '/notify',
        method: 'DELETE',
        params,
      }),
    }),
  }
}
