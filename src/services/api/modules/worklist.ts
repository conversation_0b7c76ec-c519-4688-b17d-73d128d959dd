import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'
import JSZip from 'jszip'

import { createApiQuery, buildApiUrl } from '../base/helpers'
import type { BaseRes } from '../type'
import type {
  GetWorklistGroupReq,
  GetWorklistReq,
  GetWorklistDetailReq,
  GetRsPreviewReq,
  PrioritizeWorklistGroupReq,
  PostWorklistRedrawReq,
  PostRsResendReq,
  PutWorklistReq,
  PatchWorklistGroupStatusReq,
  PatchWorklistStatusReq,
  GetWorklistGroupRes,
  GetWorklistRes,
  GetWorklistDetailRes,
} from './worklist.type'

export function createWorklistEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get worklist groups with token refresh
    getWorklistGroup: builder.query<GetWorklistGroupRes, GetWorklistGroupReq>({
      query: (params) => buildApiUrl({ url: '/worklist/group', params }),
    }),

    reGetWorklistGroup: builder.query<GetWorklistGroupRes, GetWorklistGroupReq>({
      query: (params) => buildApiUrl({ url: '/worklist/group', params }),
    }),

    // Prioritize worklist group
    patchPrioritizeWorklistGroup: builder.mutation<BaseRes, PrioritizeWorklistGroupReq>({
      query: ({ worklist_group_id }: PrioritizeWorklistGroupReq) => {
        return createApiQuery({
          url: `/worklist/group/${worklist_group_id}/insert`,
          method: 'PATCH',
        })
      },
    }),

    // Update worklist group status
    patchWorklistGroup: builder.mutation<BaseRes, PatchWorklistGroupStatusReq>({
      query: ({ worklist_group_id, status }: PatchWorklistGroupStatusReq) => {
        return createApiQuery({
          url: `/worklist/group/${worklist_group_id}/status`,
          method: 'PATCH',
          body: { status },
        })
      },
    }),

    // Get worklist with token refresh
    getWorklist: builder.query<GetWorklistRes, GetWorklistReq>({
      query: ({ worklist_group_id, ...params }) => buildApiUrl({ url: `/worklist/${worklist_group_id}`, params }),
    }),

    // Get worklist without token refresh (for polling)
    reGetWorklist: builder.query<GetWorklistRes, GetWorklistReq>({
      query: ({ worklist_group_id, ...params }) => buildApiUrl({ url: `/worklist/${worklist_group_id}`, params }),
    }),

    // Update worklist status
    patchWorklistStatus: builder.mutation<BaseRes, PatchWorklistStatusReq>({
      query: ({ id, status }: PatchWorklistStatusReq) => {
        return createApiQuery({
          url: `/worklist/${id}/status`,
          method: 'PATCH',
          body: { status },
        })
      },
    }),

    // Get worklist detail
    getWorklistDetail: builder.mutation<GetWorklistDetailRes, GetWorklistDetailReq>({
      query: ({ id }: GetWorklistDetailReq) => createApiQuery({
        url: `/worklist/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Update worklist
    putWorklist: builder.mutation<BaseRes, PutWorklistReq>({
      query: ({ id, data }: PutWorklistReq) => createApiQuery({
        url: `/worklist/${id}/update`,
        method: 'PUT',
        body: data,
      }),
    }),

    // Redraw worklist
    postWorklistRedraw: builder.mutation<BaseRes, PostWorklistRedrawReq>({
      query: ({ worklist_id, data }: PostWorklistRedrawReq) => {
        return createApiQuery({
          url: `/task/${worklist_id}/redraw`,
          method: 'POST',
          body: data,
        })
      },
    }),

    // Resend RS DICOM
    postRsResend: builder.mutation<BaseRes, PostRsResendReq>({
      query: ({ id, remote_server_id_list, folder_id_list }: PostRsResendReq) => {
        return createApiQuery({
          url: `/task/${id}/rs/resend`,
          method: 'POST',
          body: {
            remote_server_id_list,
            folder_id_list,
          },
        })
      },
    }),

    // Get RS preview images as zip
    getRsPreviewZip: builder.mutation<string[], GetRsPreviewReq>({
      query: ({ worklist_id }: GetRsPreviewReq) => ({
        url: `/worklist/${worklist_id}/images/rs/zip`,
        method: 'GET',
        responseHandler: async (response: Response) => {
          if (response.status !== 200) {
            return response.json()
          }

          const arrayBuffer = await response.arrayBuffer()
          const zip = await JSZip.loadAsync(arrayBuffer)

          const imageBlobs = await Promise.all(
            Object.keys(zip.files)
              .filter((filename) => /\.(png)$/i.test(filename))
              .map(async (filename) => {
                const file = zip.files[filename]
                const blob = await file.async('blob')
                return URL.createObjectURL(blob)
              }),
          )

          return imageBlobs
        },
        cache: 'no-cache',
      }),
    }),
  }
}
