import type { PaginationMeta, Sort<PERSON>ara<PERSON>, StudyDateRangeFilter } from '../type'

// ------------------------ request ------------------------
export interface GetWorklistGroupReq extends Sort<PERSON>ara<PERSON>, StudyDateRangeFilter {
  page?: number
  patient_id?: string
  study_status?: string
  history?: boolean
}

export interface GetWorklistReq extends SortParams {
  worklist_group_id: number
}

export interface GetWorklistDetailReq {
  id: number
}

export interface GetRsPreviewReq {
  worklist_id: number | string
}

export interface PostRsResendReq {
  id: number
  remote_server_id_list: RemoteType[]
  folder_id_list: RemoteType[]
}

type DetailTypeWithAPI = (Omit<CustomizedWorklistDetailType, 'structures'> & { structures: StructureSortType[] })

export interface PostWorklistRedrawReq {
  worklist_id: number
  data: WorklistProtocolType | DetailTypeWithAPI
}

export interface PutWorklistReq {
  id: number
  data: WorklistProtocolType | DetailTypeWithAPI
}

export interface PatchWorklistGroupStatusReq {
  worklist_group_id: number
  status: string
}

export interface PatchWorklistStatusReq {
  id: number
  status: string
}

export interface PrioritizeWorklistGroupReq {
  worklist_group_id: number
}

// ------------------------ response -----------------------
export interface GetWorklistGroupRes extends PaginationMeta {
  worklist_group: WorklistGroupType[]
}

export interface GetWorklistRes extends WorklistType { }

export interface GetWorklistDetailRes extends WorklistDetailType { }
