// ------------------------ request ------------------------
export interface GetProtocolReq {
  name?: string
}

export interface GetProtocolDetailReq {
  id: number
}

export interface GetProtocolDetailRes extends ProtocolDetailType { }

export interface CheckProtocolNameReq {
  name: string
}

export interface PostProtocolReq extends ProtocolDetailType { }

export interface PutProtocolReq extends ProtocolDetailType {
  id: number
}

export interface PutProtocolSortReq {
  protocol_sort: number[]
}

export interface PatchProtocolStatusReq {
  id: number
  status: string
  name?: string
}

export interface DeleteProtocolReq {
  id: number
}
// ------------------------ response -----------------------
export interface GetProtocolRes {
  protocols: ProtocolType[]
}

export interface GetProtocolConfigRes {
  protocols: ProtocolConfigType[]
}
