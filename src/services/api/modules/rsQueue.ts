import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import type {
  GetRsStructureRes,
} from './rs.type'
import { createApiQuery } from '../base/helpers'
import { BaseRes, ResponseData } from '../type'

export function createRsQueueEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // 取得 RS queue study列表
    getRsQueueStudies: builder.query<ResponseData<RsQueueGroupType[]>, void>({
      query: () => createApiQuery({
        url: '/rs/queue/studies',
        method: 'GET',
      }),
    }),

    // 取得 RS queue detail
    getRsQueueDetail: builder.query<ResponseData<ImageRSList[]>, { study_uid: string }>({
      query: ({ study_uid }) => createApiQuery({
        url: `/rs/queue/studies/${study_uid}/rs`,
        method: 'GET',
      }),
    }),

    // 取得 RS Structure資料
    getRsStructure: builder.query<GetRsStructureRes, { rs_id: number }>({
      query: ({ rs_id }) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}/structures`,
        method: 'GET',
      }),
    }),

    // 刪除 RS queue detail
    deleteRsQueueItem: builder.mutation<BaseRes, { rs_id: number }>({
      query: ({ rs_id }) => createApiQuery({
        url: `/rs/queue/rs/${rs_id}`,
        method: 'DELETE',
      }),
    }),
  }
}
