import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../base/helpers'
import type { BaseRes } from '../type'

interface GetStructureReq {
  name?: string
}

interface PutStructureReq {
  structures: StructureType[]
}

interface GetStructureRes {
  structures: StructureType[]
}

interface GetStructureConfigRes {
  structures: StructureConfigType[]
  category_type: string[]
  customized_structures_type: string[]
}

export function createStructureEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get structures
    getStructure: builder.mutation<GetStructureRes, GetStructureReq>({
      query: (params: GetStructureReq) => createApiQuery({
        url: '/structure',
        method: 'GET',
        params,
      }),
    }),

    // Update structures
    putStructure: builder.mutation<BaseRes, PutStructureReq>({
      query: ({ structures }: PutStructureReq) => createApiQuery({
        url: '/structure',
        method: 'PUT',
        body: { structures },
      }),
    }),

    // Get structure configuration
    getStructureConfig: builder.mutation<GetStructureConfigRes, void>({
      query: () => createApiQuery({
        url: '/structure',
        method: 'GET',
        params: { config_mode: true },
      }),
    }),
  }
}
