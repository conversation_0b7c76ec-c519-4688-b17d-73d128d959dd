// eslint-disable-next-line import/no-cycle
import { kickOff, refreshToken } from '@/store/reducers/authSlice'

// Endpoints that should not trigger token refresh
const ENDPOINTS_WITHOUT_REFRESH = [
  'postLogin',
  'deleteLogin',
  'reGetWorklistGroup',
  'reGetWorklist',
  'reGetNotify',
  'getRemoteStatus',
] as const

/**
 * Base query with authentication and token refresh logic
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const baseQueryWithReauth = (baseQuery: any) => async (args: any, api: any, extraOptions: any) => {
  const result = await baseQuery(args, api, extraOptions)

  // Handle 401 errors - kick off user
  if (result.error && result.error.status === 401) {
    api.dispatch(kickOff())
    return result
  }

  // Skip token refresh for certain endpoints
  if (ENDPOINTS_WITHOUT_REFRESH.includes(api.endpoint)) {
    return result
  }

  // Refresh token for all other endpoints
  const refreshTokenResult = await baseQuery({ url: '/login/token/refresh', method: 'GET' }, api, extraOptions)

  // If refresh token fails with 401, kick off user
  if (refreshTokenResult.error && refreshTokenResult.error.status === 401) {
    api.dispatch(kickOff())
    return result
  }

  // Update token in store if refresh was successful
  const refreshTokenData = refreshTokenResult.data as { token?: string }
  if (refreshTokenData?.token) {
    api.dispatch(refreshToken({ token: refreshTokenData.token }))
  }

  return result
}

export { baseQueryWithReauth }
