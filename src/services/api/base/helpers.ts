/**
 * Build query parameters by filtering out undefined/null values
 * @param input - Object with potential undefined/null values
 * @returns Filtered object with only defined values
 */
export function buildParams<T extends Record<string, any>>(
  input: T,
): Partial<T> {
  return Object.entries(input).reduce((acc, [key, value]) => {
    // Only include parameters that have meaningful values
    // This matches the behavior of the old API implementation
    if (value !== undefined && value !== null && value !== '') {
      (acc as any)[key] = value
    }
    return acc
  }, {} as Partial<T>)
}

/**
 * Constructs a full URL with optional query parameters.
 *
 * @param url - The base API endpoint URL.
 * @param params - Optional query parameters to include in the URL.
 * @returns A full URL string with query parameters if provided.
 */
export const buildApiUrl = ({ url, params }: { url: string, params?: Record<string, any> }): string => {
  const query = params
    ? new URLSearchParams(
      Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== null && value !== undefined && value !== '') acc[key] = String(value)
        return acc
      }, {} as Record<string, string>),
    )
    : ''

  return `${url}${query ? `?${query.toString()}` : ''}`
}

/**
 * Create a standardized API query configuration
 * @param config - API configuration object
 * @param config.url - API endpoint URL
 * @param config.method - HTTP method
 * @param config.params - Query parameters (optional)
 * @param config.body - Request body (optional)
 * @returns RTK Query configuration object
 */
export function createApiQuery(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  params?: Record<string, any>
  body?: Record<string, any>
}) {
  const {
    url, method = 'GET', params, body,
  } = config

  return {
    url,
    method,
    ...(params && { params: buildParams(params) }),
    ...(body && { body: buildParams(body) }),
  }
}

/**
 * Extract filename from Content-Disposition header
 * @param disposition - Content-Disposition header value
 * @returns Extracted filename or default
 */
export function extractFilename(disposition: string | null): string {
  if (!disposition) return 'download.zip'

  const match = disposition.match(/filename="(.+?)"/)
  return match?.[1] || 'download.zip'
}

/**
 * Create download link and trigger download
 * @param blob - File blob
 * @param filename - File name
 */
export function triggerDownload(blob: Blob, filename: string): void {
  const a = document.createElement('a')
  a.href = window.URL.createObjectURL(blob)
  a.download = filename
  a.style.display = 'none'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  window.URL.revokeObjectURL(a.href)
}
