import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import J<PERSON><PERSON><PERSON> from 'jszip'

// eslint-disable-next-line import/no-cycle
import { RootState } from '../store'
// eslint-disable-next-line import/no-cycle
import { kickOff, refreshToken } from '../store/reducers/authSlice'

type BaseQueryType = ReturnType<typeof fetchBaseQuery>

const withoutRefreshToken = [
  'postLogin',
  'deleteLogin',
  'reGetWorklistGroup',
  'reGetWorklist',
  'reGetNotify',
  'getRemoteStatus',
]

const baseQueryWithReauth: (baseQuery: BaseQueryType) => BaseQueryType = (
  baseQuery,
) => async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions)
  if (result.error && result.error.status === 401) {
    api.dispatch(kickOff())
    return result
  }
  if (withoutRefreshToken.includes(api.endpoint)) {
    return result
  }
  // refresh token
  const refreshTokenResult = await baseQuery({ url: '/login/token/refresh', method: 'GET' }, api, extraOptions)
  if (refreshTokenResult.error && refreshTokenResult.error.status === 401) {
    api.dispatch(kickOff())
    return result
  }
  const refreshTokenData = refreshTokenResult.data as { token?: string }
  api.dispatch(refreshToken({ token: refreshTokenData.token }))
  return result
}

export const legacyApi = createApi({
  baseQuery: baseQueryWithReauth(fetchBaseQuery({
    baseUrl: window.config.env.api.rest,
    // default header
    prepareHeaders: (headers, { getState }) => {
      const { token } = (getState() as RootState).authReducer
      if (token) headers.set('authorization', `Bearer ${token}`)
      headers.set('X-Frame-Options', 'DENY')
      headers.set('Content-Security-Policy', 'frame-ancestors \'none\'')
      return headers
    },
  })),
  endpoints: (builder) => ({
    // login
    postLogin: builder.mutation<LoginRes, LoginReq>({
      query: ({ account, password }: LoginReq) => {
        return {
          url: '/login',
          method: 'POST',
          body: { account, password },
        }
      },
    }),
    // logout
    deleteLogin: builder.mutation<Res, void>({
      query: () => {
        return {
          url: '/login',
          method: 'DELETE',
        }
      },
    }),
    // get notify
    getNotify: builder.mutation<GetNotifyRes, GetNotifyReq>({
      query: ({ quantity }: GetNotifyReq) => {
        const params: { quantity?: number } = {}
        if (quantity) {
          params.quantity = quantity
        }
        return {
          url: '/notify',
          method: 'GET',
          params,
        }
      },
    }),
    // recall get notify without refresh token
    reGetNotify: builder.mutation<GetNotifyRes, GetNotifyReq>({
      query: ({ quantity }: GetNotifyReq) => {
        const params: { quantity?: number } = {}
        if (quantity) {
          params.quantity = quantity
        }
        return {
          url: '/notify',
          method: 'GET',
          params,
        }
      },
    }),
    // delete notify
    deleteNotify: builder.mutation<Res, DeleteNotifyReq>({
      query: ({ notify_id }: DeleteNotifyReq) => {
        const params: { notify_id?: number } = {}
        if (notify_id) {
          params.notify_id = notify_id
        }
        return {
          url: '/notify',
          method: 'DELETE',
          params,
        }
      },
    }),
    // get worklist group
    getWorklistGroup: builder.mutation<GetWorklistGroupRes, GetWorklistGroupReq>({
      query: (params: GetWorklistGroupReq) => ({
        url: '/worklist/group',
        method: 'GET',
        params,
      }),
    }),
    // recall get worklist group without refresh token
    reGetWorklistGroup: builder.mutation<GetWorklistGroupRes, GetWorklistGroupReq>({
      query: ({
        page,
        patient_id,
        study_status,
        study_date_range_start,
        study_date_range_end,
        order_key,
        ascend,
        history,
      }: GetWorklistGroupReq) => {
        const params: {
          page?: number
          patient_id?: string
          study_status?: string
          study_date_range_start?: string
          study_date_range_end?: string
          order_key?: string
          ascend?: boolean
          history?: boolean
        } = {}
        if (page) {
          params.page = page
        }
        if (patient_id) {
          params.patient_id = patient_id
        }
        if (study_status) {
          params.study_status = study_status
        }
        if (study_date_range_start) {
          params.study_date_range_start = study_date_range_start
        }
        if (study_date_range_end) {
          params.study_date_range_end = study_date_range_end
        }
        if (order_key) {
          params.order_key = order_key
        }
        if (ascend) {
          params.ascend = ascend
        }
        if (history) {
          params.history = history
        }
        return {
          url: '/worklist/group',
          method: 'GET',
          params,
        }
      },
    }),
    // prioritize worklist group
    patchPrioritizeWorklistGroup: builder.mutation<Res, PutPrioritizeWorklistGroupReq>({
      query: ({ worklist_group_id }: PutPrioritizeWorklistGroupReq) => {
        return {
          url: `/worklist/group/${worklist_group_id}/insert`,
          method: 'PATCH',
        }
      },
    }),
    // update worklist group status
    patchWorklistGroup: builder.mutation<Res, PutWorklistGroupStatusReq>({
      query: ({ worklist_group_id, status }: PutWorklistGroupStatusReq) => {
        return {
          url: `/worklist/group/${worklist_group_id}/status`,
          method: 'PATCH',
          body: { status },
        }
      },
    }),
    // get worklist
    getWorklist: builder.mutation<GetWorklistRes, GetWorklistReq>({
      query: ({
        worklist_group_id,
        order_key,
        ascend,
      }: GetWorklistReq) => {
        const params: { order_key?: string, ascend?: boolean } = {}
        if (order_key) {
          params.order_key = order_key
        }
        if (ascend) {
          params.ascend = ascend
        }
        return {
          url: `/worklist/${worklist_group_id}`,
          method: 'GET',
          params,
        }
      },
    }),
    // recall get worklist without refresh token
    reGetWorklist: builder.mutation<GetWorklistRes, GetWorklistReq>({
      query: ({
        worklist_group_id,
        order_key,
        ascend,
      }: GetWorklistReq) => {
        const params: { order_key?: string, ascend?: boolean } = {}
        if (order_key) {
          params.order_key = order_key
        }
        if (ascend) {
          params.ascend = ascend
        }
        return {
          url: `/worklist/${worklist_group_id}`,
          method: 'GET',
          params,
        }
      },
    }),
    // update worklist status
    patchWorklistStatus: builder.mutation<Res, PatchWorklistStatusReq>({
      query: ({ id, status }: PatchWorklistStatusReq) => {
        return {
          url: `/worklist/${id}/status`,
          method: 'PATCH',
          body: { status },
        }
      },
    }),
    // get worklist detail
    getWorklistDetail: builder.mutation<GetWorklistDetailRes, GetWorklistDetailReq>({
      query: ({ id }: GetWorklistDetailReq) => {
        return {
          url: `/worklist/${id}/detail`,
          method: 'GET',
        }
      },
    }),
    // get worklist protocol detail for update use protorcol
    getWorklistProtocolDetail: builder.mutation<GetProtocolDetailRes, GetProtocolDetailReq>({
      query: ({ id }: GetProtocolDetailReq) => {
        return {
          url: `/protocol/${id}/detail`,
          method: 'GET',
        }
      },
    }),
    // update worklist
    putWorklist: builder.mutation<Res, PutWorklistReq>({
      query: ({ id, data }: PutWorklistReq) => {
        return {
          url: `/worklist/${id}/update`,
          method: 'PUT',
          body: data,
        }
      },
    }),
    // redraw worklist
    postWorklistRedraw: builder.mutation<Res, PostWorklistRedrawReq>({
      query: ({ worklist_id, data }: PostWorklistRedrawReq) => {
        return {
          url: `/task/${worklist_id}/redraw`,
          method: 'POST',
          body: data,
        }
      },
    }),
    // resend RS dicom
    postRsResend: builder.mutation<Res, PostRsResendReq>({
      query: ({ id, remote_server_id_list, folder_id_list }: PostRsResendReq) => {
        return {
          url: `/task/${id}/rs/resend`,
          method: 'POST',
          body: {
            remote_server_id_list,
            folder_id_list,
          },
        }
      },
    }),
    // get Rs Image zip
    getRsPreviewZip: builder.mutation<any, GetRsPrevew>({
      query({ worklist_id }) {
        return {
          url: `/worklist/${worklist_id}/images/rs/zip`,
          method: 'GET',
          responseHandler: async (response) => {
            if (response.status !== 200) {
              return response.json()
            }
            const arrayBuffer = await response.arrayBuffer()
            const zip = await JSZip.loadAsync(arrayBuffer)

            const imageBlobs = await Promise.all(
              Object.keys(zip.files)
                .filter((filename) => /\.(png)$/i.test(filename))
                .map(async (filename) => {
                  const file = zip.files[filename]
                  const blob = await file.async('blob')
                  return URL.createObjectURL(blob)
                }),
            )
            return imageBlobs
          },
          cache: 'no-cache',
        }
      },
    }),
    // get structure
    getStructure: builder.mutation<GetStructureRes, GetStructureReq>({
      query: ({ name }) => {
        const params: { name?: string } = {}
        if (name) {
          params.name = name
        }
        return {
          url: '/structure',
          method: 'GET',
          params,
        }
      },
    }),
    // update structure
    putStructure: builder.mutation<Res, PutStructureReq>({
      query: ({ structures }) => {
        return {
          url: '/structure',
          method: 'PUT',
          body: { structures },
        }
      },
    }),
    // get protocol
    getProtocol: builder.query<GetProtocolRes, GetProtocolReq>({
      query: ({ name }: GetProtocolReq) => ({
        url: '/protocol',
        method: 'GET',
        params: name ? { name } : undefined,
      }),
    }),
    // update protocol sort
    putProtocolSort: builder.mutation<Res, PutProtocolSortReq>({
      query: ({ protocol_sort }: PutProtocolSortReq) => ({
        url: '/protocol/sort',
        method: 'PUT',
        body: { protocol_sort },
      }),
    }),
    // update protocol status
    patchProtocolStatus: builder.mutation<Res, PatchProtocolStatusReq>({
      query: ({ id, status }: PatchProtocolStatusReq) => ({
        url: `/protocol/${id}/status`,
        method: 'PATCH',
        body: { status },
      }),
      // Temporarily removed optimistic update to avoid circular dependency
      // async onQueryStarted({ id, status }, { dispatch, getState }) {
      //   const { protocols } = (getState() as RootState).protocolReducer
      //   const newList = protocols.map((item) => (item.id === id
      //     ? { ...item, status }
      //     : item))
      //   dispatch(updateProtocols(newList))
      // },
    }),
    // delete protocol
    deleteProtocol: builder.mutation<Res, DeleteProtocolReq>({
      query: ({ id }: DeleteProtocolReq) => {
        return {
          url: `/protocol/${id}`,
          method: 'DELETE',
        }
      },
    }),
    // get protocol detail
    getProtocolDetail: builder.mutation<GetProtocolDetailRes, GetProtocolDetailReq>({
      query: ({ id }: GetProtocolDetailReq) => {
        return {
          url: `/protocol/${id}/detail`,
          method: 'GET',
        }
      },
    }),
    // get remote config
    getRemoteConfig: builder.mutation<GetRemoteConfigRes, void>({
      query: () => {
        return {
          url: '/source_destination',
          method: 'GET',
          params: { config_mode: true },
        }
      },
    }),
    // get structure config
    getStructureConfig: builder.mutation<GetStructureConfigRes, void>({
      query: () => {
        return {
          url: '/structure',
          method: 'GET',
          params: { config_mode: true },
        }
      },
    }),
    // get protocol config
    getProtocolConfig: builder.mutation<GetProtocolConfigRes, void>({
      query: () => {
        return {
          url: '/protocol',
          method: 'GET',
          params: { config_mode: true },
        }
      },
    }),
    // check protocol name
    checkProtocolName: builder.mutation<Res, CheckProtocolNameReq>({
      query: ({ name }: CheckProtocolNameReq) => {
        return {
          url: '/protocol/check',
          method: 'POST',
          body: { name },
        }
      },
    }),
    // new protocol
    postProtocol: builder.mutation<Res, PostProtocolReq>({
      query: ({
        protocol_name,
        description,
        structure_set_label,
        status,
        source,
        destination,
        structures,
        study_info,
        customized_structures,
      }: PostProtocolReq) => {
        return {
          url: '/protocol',
          method: 'POST',
          body: {
            protocol_name,
            description,
            structure_set_label,
            status,
            source,
            destination,
            structures,
            study_info,
            customized_structures,
          },
        }
      },
    }),
    // update protocol
    putProtocol: builder.mutation<Res, PutProtocolReq>({
      query: ({
        id,
        protocol_name,
        description,
        structure_set_label,
        status,
        source,
        destination,
        structures,
        study_info,
        customized_structures,
      }: PutProtocolReq) => ({
        url: `/protocol/${id}`,
        method: 'PUT',
        body: {
          protocol_name,
          description,
          structure_set_label,
          status,
          source,
          destination,
          structures,
          study_info,
          customized_structures,
        },
      }),
    }),
    // get remote
    getRemote: builder.mutation<GetRemoteRes, GetRemoteReq>({
      query: ({ type }: GetRemoteReq) => {
        const params: { type?: string } = {}
        if (type) {
          params.type = type
        }
        return {
          url: '/source_destination',
          method: 'GET',
          params,
        }
      },
    }),
    // get remote status only update status
    getRemoteStatus: builder.mutation<GetRemoteRes, GetRemoteReq>({
      query: ({ type }: GetRemoteReq) => {
        const params: { type?: string } = {}
        if (type) {
          params.type = type
        }
        return {
          url: '/source_destination',
          method: 'GET',
          params,
        }
      },
    }),
    // put remote
    putRemote: builder.mutation<Res, PutRemoteReq>({
      query: ({ type, remote_server_list, folder_list }: PutRemoteReq) => {
        return {
          url: '/source_destination',
          method: 'PUT',
          params: { type },
          body: { remote_server_list, folder_list },
        }
      },
    }),
    // post remote server
    postRemoteServer: builder.mutation<Res, PostRemoteServerReq>({
      query: ({
        type,
        name,
        description,
        ae_title,
        ip,
        port,
      }: PostRemoteServerReq) => {
        return {
          url: `/source_destination/${type}/remote_server`,
          method: 'POST',
          body: {
            name,
            description,
            ae_title,
            ip,
            port,
          },
        }
      },
    }),
    // delete remote server
    deleteRemoteServer: builder.mutation<Res, DeleteRemoteServerReq>({
      query: ({
        type, ae_title, ip, port,
      }: DeleteRemoteServerReq) => {
        return {
          url: `/source_destination/${type}/remote_server`,
          method: 'DELETE',
          body: { ae_title, ip, port },
        }
      },
    }),
    // post folder
    postFolder: builder.mutation<Res, PostFolderReq>({
      query: ({
        type,
        folder_name,
        folder_description,
        folder_path, folder_account,
        folder_password,
      }: PostFolderReq) => {
        return {
          url: `/source_destination/${type}/folder`,
          method: 'POST',
          body: {
            folder_name,
            folder_description,
            folder_path,
            folder_account,
            folder_password,
          },
        }
      },
    }),
    // delete folder
    deleteFolder: builder.mutation<Res, DeleteFolderReq>({
      query: ({ type, folder_name, folder_path }: DeleteFolderReq) => {
        return {
          url: `/source_destination/${type}/folder`,
          method: 'DELETE',
          body: { folder_name, folder_path },
        }
      },
    }),
    // download Dicom
    getDicom: builder.mutation<any, GetDicomReq>({
      query: ({ id, rs, ct }: GetDicomReq) => {
        return {
          url: `/task/${id}/download`,
          method: 'GET',
          params: { rs, ct },
          // eslint-disable-next-line consistent-return
          responseHandler: async (response) => {
            if (response.status !== 200) {
              return response.json()
            }
            const blob = await response.blob()
            const a = document.createElement('a')
            a.href = window.URL.createObjectURL(blob)

            const disposition = response.headers.get('content-disposition')
            const filename = disposition?.match(/filename="(.+?)"/)?.[1] || 'unknown.zip'

            a.download = filename
            a.style.display = 'none'
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            window.URL.revokeObjectURL(a.href)
          },
          cache: 'no-cache',
        }
      },
    }),
  }),
})

// Export the main API instance for backward compatibility
export const api = legacyApi

export const {
  usePostLoginMutation,
  useDeleteLoginMutation,
  useGetNotifyMutation,
  useReGetNotifyMutation,
  useDeleteNotifyMutation,
  useReGetWorklistGroupMutation,
  usePatchPrioritizeWorklistGroupMutation,
  usePatchWorklistGroupMutation,
  useGetWorklistMutation,
  useReGetWorklistMutation,
  usePatchWorklistStatusMutation,
  useGetWorklistDetailMutation,
  useGetWorklistProtocolDetailMutation,
  usePutWorklistMutation,
  usePostWorklistRedrawMutation,
  usePostRsResendMutation,
  useGetStructureMutation,
  usePutStructureMutation,
  // Protocol
  useLazyGetProtocolQuery,
  usePutProtocolSortMutation,
  usePatchProtocolStatusMutation,
  useDeleteProtocolMutation,
  useGetProtocolDetailMutation,
  useGetRemoteConfigMutation,
  useGetStructureConfigMutation,
  useGetProtocolConfigMutation,
  useCheckProtocolNameMutation,
  usePostProtocolMutation,
  usePutProtocolMutation,
  useGetRemoteMutation,
  useGetRemoteStatusMutation,
  useGetRsPreviewZipMutation,
  usePutRemoteMutation,
  usePostRemoteServerMutation,
  useDeleteRemoteServerMutation,
  usePostFolderMutation,
  useDeleteFolderMutation,
  useGetDicomMutation,
} = legacyApi
