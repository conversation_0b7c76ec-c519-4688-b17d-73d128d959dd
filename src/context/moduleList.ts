export type ConfigDescriptionItem = {
  label: string,
  required?: boolean
  type?: 'textarea' | 'input' | 'select' | 'switch'
  props?: {
    [key: string]: any
  }
  value?: string | number
  tooltip?: string
}

export const protocolConfigHeader: ConfigDescriptionItem[] = [
  { label: 'protocol_name', type: 'input' },
  { label: 'description', type: 'textarea' },
  { label: 'structure_set_label', type: 'input' },
  { label: 'status', type: 'switch' },
]

export const workListConfigHeader: ConfigDescriptionItem[] = [
  { label: 'patient_id', type: 'input' },
  { label: 'structure_set_label', type: 'input' },
  { label: 'use_protocol', type: 'select' },
  { label: 'study_date' },
  { label: 'study_description' },
  { label: 'protocol_description', type: 'input' },
  { label: 'series_time' },
  { label: 'series_description' },
]

export const operationBtnGroup: OperationBtnGroup[] = [
  {
    isDisabled: false,
  },
  {
    isDisabled: false,
  },
  {
    isDisabled: false,
  },
]
