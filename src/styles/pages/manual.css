/*************************** base ***************************/
.manual-wrapper {
  position: relative;
  min-width: 1200px;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
}

@media print {
  .manual-wrapper {
    display: none;
  }
}

.manual-wrapper .ant-layout-content {
  padding: 0;
}
/*************************** header ***************************/
/* .manual-wrapper-header {
  position: sticky;
  top: 0;
  z-index: 5;
  width: 100%;
  padding: 0;
  background: var(--color-gray_4);
}
.manual-wrapper-header header.head {
  width: 100%;
  padding: 0;
  background: var(--color-gray_4);
}

.manual-wrapper-header  */
header.head h1 {
  cursor: default;
}

/*************************** aside ***************************/
.manual-wrapper .manual-layout-sidebar.ant-layout-sidebar {
  background: var(--color-gray_4);
}

.faq-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.faq-btn-active {
  background-color: var(--color-gray_3);
}

.faq-icon {
  margin-top: 0.75rem;
}

.manual-wrapper .ant-layout-sider-children {
  gap: 0;
}
/* default antd anchor */
.manual-layout-sidebar-anchor.ant-anchor-wrapper {
  position: relative;
  padding: 0 20px;
}
.manual-layout-sidebar-anchor.ant-anchor-wrapper .ant-anchor .ant-anchor-link {
  padding: 0;
  padding-left: 1rem;
  font-size: 14px;
  color: var(--color-gray_0);
  line-height: 28px;
  transition: all 0.24s;
  margin-bottom: 10px;
}

.manual-layout-sidebar-anchor.ant-anchor-wrapper .ant-anchor .ant-anchor-link-title {
  padding: 5px 0;
  color: var(--color-gray_1);
}

/* hover antd anchor */
.manual-layout-sidebar-anchor.ant-anchor-wrapper .ant-anchor .ant-anchor-link:hover {
  background: var(--color-gray_2);
}
.manual-layout-sidebar-anchor.ant-anchor-wrapper .ant-anchor .ant-anchor-link:hover .ant-anchor-link-title {
  color: var(--color-gray_0);
}

/* select antd anchor */
.manual-layout-sidebar-anchor.ant-anchor-wrapper .ant-anchor .ant-anchor-link-active > .ant-anchor-link-title {
  color: var(--color-gray_0);
  font-weight: 700;
}

.manual-layout-sidebar-anchor.ant-anchor-wrapper:not(.ant-anchor-wrapper-horizontal) .ant-anchor .ant-anchor-ink.ant-anchor-ink-visible {
  background-color: var(--color-primary);
  width: 4px;
}

.manual-layout-sidebar-anchor.ant-anchor-wrapper .ant-anchor {
  padding-bottom: 200px;
}

.ant-anchor-wrapper:not(.ant-anchor-wrapper-horizontal) .ant-anchor::before {
  border-color: transparent;
}

.manual-layout-sidebar-content {
  margin-top: 16px;
  overflow: auto;
  height: calc(100% - 105px);
}
.manual-layout-sidebar-content::-webkit-scrollbar-track,
::-webkit-scrollbar-corner {
  border-radius: 10px;
  background-color: transparent;
}

.manual-layout-sidebar-content::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--color-gray_4);
}

.manual-layout-sidebar-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: var(--scroll-bar-color);
}

/************************ content tag ************************/

.manual-directions-article {
  padding-inline: 64px;
  padding-top: 30px;
}

h1.directions,
h2.directions {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-style: normal;
  font-weight: 700;
  line-height: 36px;
}

h1.directions {
  color: var(--color-primary);
}

.title-hr {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-hr::after {
  content: "";
  flex: 1;
  border: 1px solid var(--manual-border-color);
  height: 0px;
}

.ant-anchor-link-title .title-hr span {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

h2.directions,
h3.directions {
  color: var(--color-gray_0);
}

h3.directions {
  margin-bottom: 18px;
  font-size: 18px;
  font-weight: 700;
  line-height: 32px;
}

ul.directions li {
  color: var(--color-gray_1);
}

ul.directions,
p.directions {
  margin: 0 0 2.25rem;
  line-height: 32px;
}

p.directions {
  color: var(--color-gray_1);
}

p.directions.bg {
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--color-gray_2);
}

a.directions {
  color: var(--color-primary);
  text-decoration: underline;
}

a.directions:active {
  color: var(--color-primary_variants);
  text-decoration: underline;
}

figure.directions {
  margin: 0 0 2.25rem;
  max-width: 100%;
}

figure.directions img {
  max-width: 100%;
  border-radius: 5px;
  border: 1px solid var(--color-gray_1);
  overflow: hidden;
}

figure.directions figcaption {
  margin-top: 8px;
  font-size: 14px;
  color: var(--color-gray_1);
}

/************************ FAQ ************************/
#FAQ-wrapper {
  padding-top: 68px;
  padding-bottom: 200px;
  padding-inline: 80px;
  white-space: pre-wrap;
}
.FAQ-title h2 {
  font-size: 2.25rem;
  color: var(--color-gray_0);
  text-align: center;
}

.FAQ-title p {
  font-size: 1rem;
  text-align: center;
  line-height: 2.25;
}

/*** collapse ***/
#FAQ-wrapper .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  color: var(--color-gray_0);
}

.ant-collapse > .ant-collapse-item .ant-collapse-collapsible-header .ant-collapse-header-text {
  flex: auto;
}

.FAQ-content {
  margin-top: 1.5rem;
}

.rotate-0 {
  transform: rotate(0deg);
  transition: transform 0.2s;
}

.rotate-90 {
  transform: rotate(-90deg);
  transition: transform 0.2s;
}
