.login-page {
  position: relative;
  height: 100vh;
  width: 100vw;
  background: var(--color-gray_5);
  overflow: hidden;

  &::before,
  &::after {
    content: "";
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
    right: 0;
  }

  &::after {
    left: 50%;
    transform: rotate(-180deg);
    background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) bottom right / 50% 50% no-repeat,
      linear-gradient(to bottom left, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) bottom left / 50% 50% no-repeat,
      linear-gradient(to top left, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) top left / 50% 50% no-repeat,
      linear-gradient(to top right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) top right / 50% 50% no-repeat,
      linear-gradient(to bottom right, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) bottom right / 50% 50% no-repeat,
      linear-gradient(to bottom left, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) bottom left / 50% 50% no-repeat,
      linear-gradient(to top left, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) top left / 50% 50% no-repeat,
      linear-gradient(to top right, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) top right / 50% 50% no-repeat;
  }

  &::before {
    left: -50%;
    background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) bottom right / 50% 50% no-repeat,
      linear-gradient(to bottom left, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) bottom left / 50% 50% no-repeat,
      linear-gradient(to top left, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) top left / 50% 50% no-repeat,
      linear-gradient(to top right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 50%) top right / 50% 50% no-repeat,
      linear-gradient(to bottom right, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) bottom right / 50% 50% no-repeat,
      linear-gradient(to bottom left, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) bottom left / 50% 50% no-repeat,
      linear-gradient(to top left, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) top left / 50% 50% no-repeat,
      linear-gradient(to top right, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%) top right / 50% 50% no-repeat;
  }

  & main::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: url("../src/assets/login-bgImg.png"), lightgray 0% 0% / 100px 100px repeat;
    mix-blend-mode: overlay;
    opacity: 0.5;
  }

  .ant-layout-content {
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .ant-form {
    padding: 5.5rem 0;
    position: relative;
    z-index: 3;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    border-radius: 0.5rem;
    background: var(--color-gray_5);
    box-shadow: 0px 0px 120px 0px rgba(255, 255, 255, 0.3);
  }

  .form-input-group {
    padding: 0 8rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .ant-form-item {
    margin: 0;
    flex: 1 0;
  }
}
.error-prompt {
  margin-bottom: 0.75rem;
  color: var(--color-error);

  .error-prompt-content {
    position: relative;
    text-wrap: nowrap;
  }
}

.login-footer {
  position: absolute;
  bottom: 0;
  z-index: 2;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 0 0 2rem;
  color: var(--color-gray_0);
  font-size: 0.75rem;

  .tag {
    padding: 0 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 0, 0, 0.3);
    background: var(--color-gray_1);
    color: var(--color-gray_5);
  }
}

.system-title {
  color: var(--color-gray_0);
  font-size: 3rem;
  font-weight: bold;
  line-height: 3.75rem;
  text-align: center;
  margin-bottom: 3rem;
}

.ant-form-item-control .ant-form-item-explain.ant-form-item-explain-connected:not(#login_account_help) {
  position: relative;
}
