/* ========================================
   History Worklist Table Styles
   ======================================== */

/* ========================================
   Base Container & Layout
   ======================================== */
#history-worklist-table {
  height: 100%;
  background: transparent;
}

/* Scrollbar styling for webkit browsers */

#history-worklist-table .ant-table-body::-webkit-scrollbar-track {
  background: var(--color-gray_1_variants);
  border-radius: 0;
}

/* Firefox scrollbar styling */
#history-worklist-table .ant-table-body {
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray_5) var(--color-gray_1_variants);
}

#history-worklist-table .ant-table-body {
  height: calc(100% - 36px);
}

#history-worklist-table .ant-table-container {
  height: 100%;
}

#history-worklist-table .ant-table-cell {
  border-bottom-width: 0;
}

#history-worklist-table li {
  list-style: none;
}

.ant-table-row,
.ant-table-cell {
  position: relative;
}

/* ========================================
   Study Row Styles (First Level)
   ======================================== */
#history-worklist-table .study-row > td {
  padding: 0;
  color: var(--color-gray_1) !important;
}

#history-worklist-table .study-row > td > svg {
  margin-top: 8px;
}

.study-row-operation {
  min-width: 152px;
  margin-top: 1rem;
  padding: 4px 40px;
  font-size: 16px;
  font-weight: 700;
  color: var(--color-gray_1);
  overflow: hidden;
  background: var(--color-gray_2);
  border: 1px solid var(--color-gray_1_variants);
  border-radius: 4px;
}

#history-worklist-table .study-row:nth-child(2) .study-row-operation {
  margin-top: 0;
}

#history-worklist-table .study-row:nth-child(2) > td > svg {
  margin-top: 0;
}

#history-worklist-table .study-row:has(+ .series-row) .study-row-operation {
  border-radius: 4px 4px 0 0;
}

/* ========================================
   Series Row Styles (Second Level)
   ======================================== */
#history-worklist-table .series-row > td {
  background: var(--color-gray_3);
  border-bottom: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .series-row td:first-child {
  border-left: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .series-row td:last-child {
  border-right: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .series-row :is(td:first-child, td:last-child) {
  background: var(--color-gray_3);
}

#history-worklist-table .series-row :is(td:first-child, td:last-child)::before {
  content: '';
  position: absolute;
  bottom: 0;
  height: 100%;
  width: calc(100% - 8px);
  border: 0 solid var(--color-gray_1_variants);
}

/* ========================================
   Instance Row Styles (Third Level)
   ======================================== */
#history-worklist-table .instance-row > td {
  background: var(--color-gray_4);
  border-bottom: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .instance-row td:first-child {
  border-left: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .instance-row td:last-child {
  border-right: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .instance-row :is(td:first-child, td:last-child) {
  background: var(--color-gray_3);
}

#history-worklist-table .instance-row :is(td:first-child, td:last-child)::before {
  content: '';
  position: absolute;
  top: 0;
  height: 100%;
  width: calc(100% - 8px);
  border: 0 solid var(--color-gray_1_variants);
  background: var(--color-gray_4);
  border-bottom-width: 1px;
}

/* Shared positioning for series and instance row borders */
#history-worklist-table :is(.series-row, .instance-row) :is(td:first-child)::before {
  right: 0;
}

#history-worklist-table :is(.series-row, .instance-row) :is(td:last-child)::before {
  left: 0;
}

/* ========================================
   No RS Row Styles
   ======================================== */
#history-worklist-table .no-rs-row > td {
  padding: 0 0.5rem;
  border: 1px solid var(--color-gray_1_variants);
  border-top: 0;
  border-bottom: 0;
}

#history-worklist-table .no-rs-row > td > div {
  padding: 1.25rem 0 1.25rem 2.75rem;
  background: var(--color-gray_4);
}

#history-worklist-table .no-rs-row:not(:has(+ .study-row)) > td > div {
  border-bottom: 1px solid var(--color-gray_1_variants);
}

#history-worklist-table .no-rs-row:has(+ .study-row) > td {
  border-bottom: 1px solid var(--color-gray_1_variants);
}

/* ========================================
   Border Radius & Container Styling
   ======================================== */
/* Study container border radius */
#history-worklist-table :is(.series-row, .instance-row, .no-rs-row):has(+ .study-row) > td:first-child {
  border-bottom-left-radius: 4px;
}

#history-worklist-table :is(.series-row, .instance-row, .no-rs-row):has(+ .study-row) > td:last-child {
  border-bottom-right-radius: 4px;
}

/* Border width adjustments for study containers */
#history-worklist-table :is(.series-row, .instance-row):has(+ .study-row) :is(td:first-child, td:last-child) {
  border-bottom-width: 1px;
}

#history-worklist-table :is(.series-row, .instance-row):not(:has(+ .study-row)) :is(td:first-child, td:last-child) {
  border-bottom-width: 0;
}

#history-worklist-table :is(.series-row, .instance-row):has(+ .study-row) :is(td:first-child, td:last-child)::before {
  border-bottom-width: 0;
}

#history-worklist-table :is(.series-row, .instance-row):not(:has(+ .study-row)) :is(td:first-child, td:last-child)::before {
  border-bottom-width: 1px;
}

/* Last row styling */
#history-worklist-table tr:last-child:not(.study-row) > td:first-child {
  border-bottom-left-radius: 4px;
  border-bottom-width: 1px;
}

#history-worklist-table tr:last-child:not(.study-row) > td:last-child {
  border-bottom-right-radius: 4px;
  border-bottom-width: 1px;
}

#history-worklist-table tr:last-child:not(.study-row) > :is(td:first-child, td:last-child)::before {
  border-bottom-width: 0;
}

/* table head */
#history-worklist-table .ant-table-thead > tr > :is(th, td) {
  padding: 1rem;
  font-weight: 700;
}
