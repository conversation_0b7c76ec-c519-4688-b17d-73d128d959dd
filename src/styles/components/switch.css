.switch {
  position: relative;
  display: block;
  width: 2rem;
  height: 1.25rem;
  border-radius: 100px;
  background: var(--color-gray_1);
  outline: 1px solid var(--color-gray_0);
  cursor: pointer;

  & input[type='checkbox'] {
    width: 100%;
    height: 100%;
    visibility: hidden;
  }

  &:after {
    content: '';
    position: absolute;
    top: 4px;
    left: 5px;
    width: 0.75rem;
    height: 0.75rem;
    background: var(--color-gray_0);
    border-radius: 90px;
    transition: 0.2s;
    &:after {
      width: 10px;
    }
  }

  &:has(input:checked) {
    background: var(--color-gray_4);
    outline: 1px solid var(--color-gray_0);
    transition: 0.2s;
    &:after {
      left: 15px;
    }
  }

  &:has(.switch:disabled) {
    opacity: 0.3;
  }
}
