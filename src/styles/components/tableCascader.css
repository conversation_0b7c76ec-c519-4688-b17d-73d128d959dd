.ant-spin-nested-loading {
  height: 100%;
}
.ant-spin-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ant-pagination.ant-table-pagination .ant-pagination-item {
  border: transparent;
}

.ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover a {
  color: var(--color-primary);
}

.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell)::before,
.ant-table-thead > tr > td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell)::before {
  height: 0 !important;
}
