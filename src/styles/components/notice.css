.notice-card {
  position: relative;
  border-radius: 2px;
  background: var(--color-gray_3);
  color: var(--color-gray_0);
  margin-top: 12px;

  .close-btn {
    position: absolute;
    right: 0;
    border: none;
    background: transparent;
    box-shadow: none;
    font-size: 10px;

    &:hover {
      background: transparent;
    }
  }

  & h5 {
    font-size: 0.75rem;
    word-break: break-all;
    color: rgba(192, 192, 192, 0.5);
  }
  .card-icon-box {
    width: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .card-body {
    display: flex;
    gap: 0.75rem;
    padding: 1.25rem 1rem 1rem;
    font-size: 0.75rem;
    border-bottom: 2px solid var(--color-gray_2);
    & hgroup {
      display: flex;
      /* align-items: center; */
      gap: 0.5rem;
      flex: 2;
      :is(h5, p) {
        margin: 0;
      }
      :is(h5) {
        flex-shrink: 0;
      }
      :is(p) {
        word-break: break-all;
      }
    }
  }

  .card-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .card-text {
    color: rgba(192, 192, 192, 0.75);
  }

  .card-footer {
    padding: 0.5rem 0.75rem;
    text-align: right;
    color: rgba(192, 192, 192, 0.5);
    font-size: 10px;
  }
}

.ant-drawer .ant-drawer-close {
  margin: 0;
  padding: 0;

  &:hover {
    background: transparent;
  }
}
.ant-drawer .ant-drawer-header .ant-drawer-header-title {
  flex-direction: row-reverse;
}

.ant-drawer-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  & h2 {
    margin: 0;
  }
}
