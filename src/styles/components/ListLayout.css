.list-item-flex-shrink {
  justify-content: start !important;

  & > div {
    flex: 5;
    padding: 0 0.75rem;

    &:first-child {
      flex: 3;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      & > div {
        flex: 1;
      }
    }

    &:last-child {
      flex: 0 0 80px;
    }
  }
}

:is(.list-item-flex-grow, .list-item-flex-shrink) div:last-child:has(.ant-list-delete) {
  padding: 0;
  padding-left: 1rem;
  justify-content: flex-end;
}

.list-item-flex-grow {
  justify-content: space-between !important;
  gap: 0px;

  & > div:not(:last-child) {
    min-width: 180px;
    flex: 5;
    margin: 0;
    padding: 0 0.75rem;

    &:first-child {
      flex: 3;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      & > div {
        flex: 1;
      }
    }
  }
  & > div:last-child {
    flex: 0 0 80px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  &:hover {
    background: var(--color-gray_2);
  }
}

.add-list-form {
  overflow-x: auto;
  .list-item-flex-grow,
  .list-item-flex-shrink {
    min-width: 990px;
  }
}
.add-list-form .ant-row:is(.list-item-flex-grow, .list-item-flex-shrink):hover {
  background: transparent;
}

.overflow-ellipsis {
  & div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}

.traffic-light {
  display: block;
  border-radius: 50%;
  height: 8px;
  width: 8px;
  &.red {
    background: var(--color-error);
  }
  &.green {
    background: var(--color-success);
  }
}

.ant-list-delete {
  background: transparent;
  cursor: pointer;
  outline: none !important;
  border: none !important;

  &:hover {
    background: transparent !important;
  }
}

.list-item-input {
  position: relative;
  padding: 0.25rem 0.5rem;
  background-color: transparent;
  border-color: transparent;
  &:focus,
  &:hover {
    border-color: transparent;
    box-shadow: none;
    border-color: var(--color-primary);
  }
}

.list-item-input.input-error {
  border-color: var(--color-error);
}

.form-item-content-justify-center .ant-form-item-control-input-content {
  justify-content: center;
}

.destination-setting-list.ant-list {
  height: 100%;

  :is(li.ant-list-item) {
    margin-bottom: 4px;
    transition: all 0.3s;
  }
}

.destination-setting-list .rc-virtual-list-holder {
  padding: 20px;
  background: var(--color-gray_2);
  border-radius: 0 0.25rem 0.25rem 0.25rem;
}
.destination-setting-list.ant-list .ant-collapse > .ant-collapse-item > .ant-collapse-header > .ant-collapse-header-text {
  flex: none;
}

.ant-collapse .ant-collapse-header.collapsed-header-hover:hover {
  color: var(--color-primary);

  .ant-checkbox .ant-checkbox-inner {
    border-color: var(--color-primary);
  }
}

.checklist-checkbox-item {
  width: 100%;
  padding: 6px 0.5rem;
  border-radius: 4px;

  &.checked {
    background: var(--color-gray_3);
  }

  &:hover {
    color: var(--color-primary);
    border-radius: 4px;
  }
}
