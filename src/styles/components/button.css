
button {
  text-align: center;
}
button.btn {
  border-radius: 5px;
  background-color: var(--color-primary);
  padding: 0.25rem 1rem;
  font-size: 16px;
  box-shadow: none;
  outline: none;
  border: none;
  color: var(--color-gray_0);
  cursor: pointer;
  transition: all 0.24s;

  &:hover {
    background-color: var(--color-primary_variants);
    color: var(--color-gray_0);
  }

  &:active {
    background-color: var(--color-primary);
  }

  &.error {
    background-color: var(--color-error);
    color: var(--color-gray_0);
  }

  &:disabled {
    color: var(--color-gray_1);
    outline: 1px solid var(--color-primary_variants);
    background-color: var(--color-primary_variants);
    cursor: not-allowed;
  }
}

button.btn.gray {
  color: var(--color-gray_0);
  background-color: var(--color-gray_1_variants);
  box-shadow: 0px 2px 2px 1px rgba(26, 38, 47, 0.4);
  transition: all 0.2s;

  &:hover {
    background-color: var(--color-gray_2);
  }

  &:disabled {
    background: var(--color-gray_2);
    box-shadow: none;
    outline: none;
  }
}

button.btn.gray-light {
  background-color: var(--color-gray_1_variants);
  box-shadow: 0px 2px 2px 1px rgba(26, 38, 47, 0.4);
  transition: all 0.2s;

  &:hover {
    background-color: var(--color-gray_2);
  }
  &:disabled {
    background: rgba(87, 96, 103, 0.4);
    box-shadow: none;
    outline: none;
  }
}

/* 外框樣式的按鈕 */
button.btn.outline, 
button.btn.outline.ant-btn-default.ant-btn-variant-outlined {
  background: transparent;
  color: var(--color-primary);
  outline: 1px solid var(--color-primary);

  &:hover {
    background: var(--color-primary_variants);
    color: var(--color-gray_0);
    outline: 1px solid var(--color-primary_variants);
  }

  &:disabled {
    color: var(--color-gray_1_variants);
    background-color: var(--color-gray_2);
    outline: 0px solid transparent;
  }
}

button.btn.outline.gray,
button.btn.outline.gray.ant-btn-default.ant-btn-variant-outlined {
  color: var(--color-gray_1);
  outline: 1px solid var(--color-gray_1);

  &:hover {
    background: var(--color-gray_1_variants);
    color:white;
    outline: 1px solid var(--color-gray_1_variants);
  }

  &:disabled {
    color: var(--color-gray_1_variants);
    background-color: var(--color-gray_2);
    outline: 0px solid transparent;
  }
}

button.btn.icon-only,
a.icon-only {
  width: 30px;
  height: 30px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: center;

  border: 0;
  outline: 0;
  color: var(--color-gray_0);
  background-color: var(--color-gray_1_variants);
  box-shadow: 0px 2px 2px 1px rgba(26, 38, 47, 0.4);

  &:hover {
    background: var(--color-gray_2);
  }

  &:disabled {
    color: var(--color-gray_1_variants);
    background-color: var(--color-gray_2);
    outline: 0px solid transparent;
  }
}

.icon-only.ant-btn.ant-btn-icon-only.ant-btn-color-default.ant-btn-variant-outlined {
  box-shadow: 0px 2px 2px 1px rgba(26, 38, 47, 0.4);
}
.icon-only.ant-btn.ant-btn-icon-only.ant-btn-color-default.ant-btn-variant-outlined:disabled {
  color: var(--color-gray_1_variants);
  background-color: var(--color-gray_2);
  border-color: transparent;
  outline: 0px solid transparent;
}

.btn.btn-link {
  padding: 0;
  color: rgba(255, 255, 255, 0.5);
  text-align: start;
}

.btn-link:hover {
  color: var(--color-primary) !important;
  background: transparent !important;
}

.btn-link-active {
  color: var(--color-primary);
  font-weight: 900;
}

.icons-btn-group {
  display: flex;
  gap: 0.75rem;
}

.btn-bg-none {
  background: transparent !important;
}

.btn-hover-gray:hover {
  background: var(--color-gray_4) !important;
}

button.link-btn {
  background: transparent;
  color: var(--color-primary);

  &:hover,
  &:active {
    background: transparent;
    color: var(--color-primary_variants);
  }
}

.ant-btn-color-primary.ant-btn-variant-outlined:disabled {
  opacity: 0.45;
}