/* transfer components */
.ant-tree .ant-tree-node-content-wrapper:hover,
.ant-tree .ant-tree-checkbox + span {
  transition: all 0s;
  &:hover {
    background: transparent;
  }
}

.ant-tree .ant-tree-checkbox-wrapper:not(.ant-tree-checkbox-wrapper-disabled):hover .ant-tree-checkbox-inner,
.ant-tree .ant-tree-checkbox:not(.ant-tree-checkbox-disabled):hover .ant-tree-checkbox-inner {
  border: 1px solid var(--color-primary);
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected,
.ant-tree .ant-tree-checkbox + span.ant-tree-node-selected {
  background: transparent;
}
