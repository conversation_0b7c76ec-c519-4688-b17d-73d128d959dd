import { read, utils } from 'xlsx'

type WorksheetName = 'direction' | 'FAQ'

const { manual, about } = window.config.env

async function fetchManualExcel<T extends WorksheetName>(
  worksheet?: T,
): Promise<T extends 'direction' ? ManualJSONData[] : FaqJSONData[]> {
  try {
    const res = await fetch(`/docs/${manual.fileName}`)
    const buffer = await res.arrayBuffer()
    const wb = read(buffer, { type: 'buffer' })
    const wsname = manual.sheet[worksheet || 'direction']
    const ws = wb.Sheets[wsname]

    let data: unknown
    if (worksheet === 'direction') {
      data = utils.sheet_to_json<ManualJSONData>(ws)
    } else {
      data = utils.sheet_to_json<FaqJSONData>(ws)
    }

    return data as T extends 'direction' ? ManualJSONData[] : FaqJSONData[]
  } catch (error) {
    return []
  }
}

async function fetchAboutExcel() {
  const res = await fetch(`/docs/${about.fileName}`)
  const buffer = await res.arrayBuffer()
  const wb = read(buffer, { type: 'buffer' })
  const name = about.sheet.container
  const ws = wb.Sheets[name]
  const data = {
    container: utils.sheet_to_json<AboutList>(ws),
  }
  return data
}

export { fetchManualExcel, fetchAboutExcel }
