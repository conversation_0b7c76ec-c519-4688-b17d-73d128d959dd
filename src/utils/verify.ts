import {
  capitalize, map, filter, uniq, uniqBy, isNil,
  some, isInteger, includes, values, flatMap,
  isString,
  isEmpty,
} from 'lodash-es'

import i18n from 'src/i18n'

const { marginValueLimit } = window.config.env.configPage

export const requiredRules = (feildName?: string) => {
  return {
    required: true,
    message: feildName ? i18n.t('form_rules.enter_variable', {
      variable: i18n.t(`plain_texts.${feildName}`),
      joinArrays: ' ',
    }) : '',
  }
}

export const duplicatedRules = (fieldName: string, data: any[]) => {
  return {
    async validator(_: any, inputValue: string | number) {
      // If inputValue exists and duplicate found in data, reject with error
      if (inputValue && data.some((row: any) => row[fieldName] === inputValue)) {
        // Generate error message with capitalized fieldName
        const errorMessage = i18n.t('form_rules.variable_unique', {
          variable: capitalize(i18n.t(`plain_texts.${fieldName}`)),
          joinArrays: ' ',
        })
        return Promise.reject(new Error(errorMessage))
      }
      return Promise.resolve()
    },
  }
}

export const dicomTagRules = () => {
  return {
    pattern: /^\([a-zA-Z0-9]{4},[a-zA-Z0-9]{4}\)$/,
    message: i18n.t('form_rules.dicom_tag_constraint'),
  }
}

export const minLengthRules = (feildName: string, min: number) => {
  return {
    min,
    message: i18n.t('form_rules.min_length', {
      variable: i18n.t(`titles.${feildName}`),
      min_length: min,
      joinArrays: ' ',
    }),
  }
}

export const getMaxLimitation = (name: string = '') => name.length > 16

export const maxLengthRules = (feildName: string, max: number) => {
  return {
    max,
    message: i18n.t('form_rules.max_length', {
      variable: i18n.t(`titles.${feildName}`),
      max_length: max,
      joinArrays: ' ',
    }),
  }
}

export const hasDuplicateName = (list: { name?: string }[]): boolean => {
  // Extract names and filter out falsy values (undefined, null, empty string)
  const names = filter(map(list, 'name'), (name) => !!name)
  return uniq(names).length !== names.length
}

export const marginValueCheck = (value: number | null) => {
  return value === null || value < 0 || value > marginValueLimit || !isInteger(value)
}

type DuplicateStucturesType = {
  custom: CustomizedStructuresType[],
  origin: StructureConfigType[]
}

// if state been empty, return true
export const verifyDetailState = {
  required: (value?: string) => !isString(value) || isEmpty(value),
  dicomTag(dicomTag: string) {
    const pattern = /^\([a-zA-Z0-9]{4},[a-zA-Z0-9]{4}\)$/
    return !pattern.test(dicomTag)
  },
  remote: (remote: RemoteCategoryType) => values(remote).every((value) => value.length === 0),
  destination({ folder, remote_server }: RemoteCategoryType) {
    const verifyFolder = folder.some((item) => !item.ct_transfer && !item.rs_transfer)
    const verifyRemoteServer = remote_server.some((item) => !item.ct_transfer && !item.rs_transfer)
    return verifyFolder || verifyRemoteServer
  },
  studyInfo(studyInfo: StudyInformationType[]) {
    if (!studyInfo.length) return true
    return studyInfo.some(({ dicom_tag }) => !dicom_tag || this.dicomTag(dicom_tag))
  },
  getCustomizedStructuresDuplicateNameErrors(customList: CustomizedStructuresType[]) {
    const names = map(customList, 'name').filter((name) => !!name)
    // Find duplicate names (may appear multiple times)
    const duplicateNames = names.filter((name, index, self) => self.indexOf(name) !== index)
    const uniqueDuplicateNames = uniq(duplicateNames)
    return filter(customList, (item) => item.name && uniqueDuplicateNames.includes(item.name))
  },
  getStructuresDuplicateNameErrors({ origin, custom }: DuplicateStucturesType) {
    const originNames = flatMap(origin, ({ efai_structure_name, customized_name }) => (
      customized_name ? [efai_structure_name, customized_name] : [efai_structure_name]
    ))
    return filter(custom, (item) => item.name && includes(originNames, item.name))
  },
  getDuplicateNameErrors({ origin, custom }: DuplicateStucturesType): CustomizedStructuresType[] {
    const err1 = this.getCustomizedStructuresDuplicateNameErrors(custom)
    const err2 = this.getStructuresDuplicateNameErrors({ origin, custom })
    return uniqBy([...err1, ...err2], 'id') as CustomizedStructuresType[]
  },
  getInputDuplicateNameError(
    record: CustomizedStructuresType,
    duplicateStructures: DuplicateStucturesType,
  ): boolean {
    // Check if the current record is duplicated among the structures at input time
    const duplicateErrors = this.getDuplicateNameErrors(duplicateStructures)
    return duplicateErrors.some((item) => item.id === record.id)
  },
  getOperationsErrors(
    item: CustomizedStructuresType,
    validStructure: number[],
  ) {
    // 沒有選擇 mode 或者 margin value 的值沒有在 select Structures list 當中
    const marginValueNoIncludeStructures = !item.structure_operations?.every((x) => validStructure.includes(x))

    switch (item.mode) {
      case 'COMBINE':
      case 'CROP':
      case 'OVERLAP':
        return item.structure_operations?.filter(Boolean).length !== 2
          || marginValueNoIncludeStructures
      case 'INNER':
      case 'OUTER':
        return !item.structure_operations?.length
          || isNil(item.margin_value)
          || some(item.margin_value || [], (value) => isNil(value))
          || marginValueNoIncludeStructures
      case 'EMPTY_STRUCTURE':
        return false
      default:
        return true
    }
  },
  getCustomizedRequiredItemError(
    item: CustomizedStructuresType,
    validStructure: number[] = [],
  ) {
    if (!item.name || !item.type || this.getOperationsErrors(item, validStructure)) {
      return item
    }
    return false
  },
  getCustomizedRequiredErrors(
    list: CustomizedStructuresType[],
    validStructure: number[] = [],
  ) {
    return list
      .filter((item) => this.getCustomizedRequiredItemError(item, validStructure))
  },
  getLimitationErrors(structures: CustomizedStructuresType[]) {
    return structures.filter((item) => getMaxLimitation(item.name)
      || some(item.margin_value || [], marginValueCheck))
  },
  customizedStructures(
    structures: DuplicateStucturesType,
    validStructure: number[],
  ) {
    const duplicateErrors = this.getDuplicateNameErrors(structures)
    const requiredErrors = this.getCustomizedRequiredErrors(structures.custom, validStructure)
    const limitationErrors = this.getLimitationErrors(structures.custom)

    return uniqBy([...duplicateErrors, ...requiredErrors, ...limitationErrors], 'id')
  },
}
