import type { AnchorLinkItemProps } from 'antd/es/anchor/Anchor'

import type { MenuItems } from 'src/typings/menu.type'

const formatList = (): ListElement => ({
  tag: 'ul',
  children: [],
})

const createImg = (item: ManualJSONData): ItemElement => ({
  tag: 'figure',
  children: item.content,
  figcaption: item.figcaption,
})

const handlePushElement = (
  element: BaseElement,
  list: ListElement,
  elements: BaseElement[],
): void => {
  if (list.children.length > 0) {
    elements.push(list, element)
  } else {
    elements.push(element)
  }
}

const createElement = (
  item: ManualJSONData,
  list: ListElement,
): BaseElement | null => {
  switch (item.tag) {
    case 'ul':
      list.children.push({ tag: 'li', children: item.content })
      return null
    case 'img':
      return createImg(item)
    case undefined:
      list.children.push({ tag: 'li', children: item.content })
      return null

    default:
      return { tag: item.tag, children: item.content }
  }
}

export const parseElements = async (data: ManualJSONData[]): Promise<BaseElement[]> => {
  let list = formatList()
  const elements: BaseElement[] = []

  data.forEach((item, index) => {
    const lastChild = (index + 1) === data.length
    const element = createElement(item, list)
    if (element) {
      handlePushElement(element, list, elements)
      if (list.children.length > 0) list = formatList()
    }
    if (lastChild && list.children.length > 0) {
      elements.push(list)
    }
  })

  return elements
}

const createPushItemHandler = (items: AnchorLinkItemProps[], defaultOpenKeys: string[]) => {
  return (item: AnchorLinkItemProps) => {
    items.push(item)
    defaultOpenKeys.push(`${item?.key}`)
  }
}

export const parseMenu = async (data: BaseElement[]): Promise<MenuItems> => {
  const items: AnchorLinkItemProps[] = []
  const defaultOpenKeys: string[] = []
  const handlePushItem = createPushItemHandler(items, defaultOpenKeys)

  data.forEach((item, index) => {
    const createdId = (num: number) => `H${num}-${item.children}-${index}`

    switch (item.tag) {
      case 'h1':
        handlePushItem({
          key: `H1-${item.children}-${index}`,
          href: `#${createdId(1)}`,
          title: <span className="title-hr gap-2"><span>{item.children}</span></span>,
        })
        break
      case 'h2':
        handlePushItem({
          key: createdId(2),
          href: `#${createdId(2)}`,
          title: <span style={{ paddingLeft: '32px' }}>{item.children}</span>,
        })
        break
      case 'h3':
        handlePushItem({
          key: createdId(3),
          href: `#${createdId(3)}`,
          title: <span style={{ paddingLeft: '64px' }}>{item.children}</span>,
        })
        break
      default:
        break
    }
  })

  return { items, defaultOpenKeys }
}
