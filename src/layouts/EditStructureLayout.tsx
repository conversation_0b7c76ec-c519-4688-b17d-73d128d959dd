import { useState } from 'react'

import { Flex, type TabsProps } from 'antd'

import EditStructureContent from 'src/components/EditStructureContent'
import ConfigSubTabs from 'src/components/Tabs/ConfigSubTabs'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { color } from 'src/utils/variables'

type SubTabItems = 'all' | 'checked'

interface Props {
  structureData: number[]
  handleUpdateStructures: (structures: number[] | number) => void
}

function EditStructureLayout({ structureData, handleUpdateStructures }: Props) {
  const [selectedTabItem, setSelectedTabItem] = useState<SubTabItems>('all')
  const { structureConfig, categoryType } = useAppSelector((state) => state.configReducer)

  const tabItems: TabsProps['items'] = [
    {
      key: 'all',
      label: <div style={{ color: color.gray[0].default }}>{i18n.t('buttons.all_structure')}</div>,
      children: (
        <EditStructureContent
          selectedRadio="all"
          selectedStructures={structureData}
          structureConfig={structureConfig}
          categoryType={categoryType}
          handleUpdateStructures={handleUpdateStructures}
        />
      ),
    }, {
      key: 'checked',
      label: (
        <Flex align="center" gap={8} style={{ color: color.gray[0].default }}>
          {i18n.t('buttons.selected')}
          <span className="check-count-ball">{structureData.length}</span>
        </Flex>
      ),
      children: (
        <EditStructureContent
          selectedRadio="checked"
          selectedStructures={structureData}
          structureConfig={structureConfig}
          categoryType={categoryType}
          handleUpdateStructures={handleUpdateStructures}
        />
      ),
    },
  ]

  return (
    <ConfigSubTabs
      activeKey={selectedTabItem}
      items={tabItems}
      onChange={(value) => setSelectedTabItem(value as SubTabItems)}
    />
  )
}

export default EditStructureLayout
