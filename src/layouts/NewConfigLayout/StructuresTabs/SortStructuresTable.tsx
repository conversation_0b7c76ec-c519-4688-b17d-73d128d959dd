import type { DragEndEvent } from '@dnd-kit/core'
import { arrayMove } from '@dnd-kit/sortable'
import { Flex, TableColumnType, Tag } from 'antd'

import SortTable, { DragHandle } from 'src/components/Table/SortTable'
import ColorLabel from 'src/components/Tag/ColorLabel'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

export type SortableDataType = CustomizedStructuresType & {
  rowKey: string
}

type Props = {
  dataSource: SortableDataType[]
  onDragChange: (data: SortableDataType[]) => void
}

function SortStructuresTable({ dataSource, onDragChange }: Props) {
  const columns: TableColumnType<SortableDataType>[] = [
    {
      key: 'darg',
      render: () => <DragHandle />,
      width: 50,
    },
    {
      key: 'sort',
      title: i18n.t('titles.sort_number'),
      dataIndex: 'sort',
      width: 120,
      render: (_a, _b, index) => index + 1,
    },
    {
      key: 'name',
      dataIndex: 'name',
      title: i18n.t('titles.structure_id'),
      render(name, record) {
        return (
          <Flex align="center" gap={4}>
            <ColorLabel
              half={'mode' in record && record.mode === 'EMPTY_STRUCTURE'}
              style={{ color: record.color_code, fontSize: 18 }}
            />
            {name}
            {'show' in record && (
              <Tag style={{
                paddingInline: 12,
                borderRadius: 50,
                fontSize: 12,
                lineHeight: '20px',
                cursor: 'default',
              }}
              >
                {i18n.t('titles.processed')}
              </Tag>
            )}
          </Flex>
        )
      },
    },
  ]

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = dataSource.findIndex((i) => i.sort === active?.id)
      const overIndex = dataSource.findIndex((i) => i.sort === over?.id)
      const newData = arrayMove(dataSource, activeIndex, overIndex).map((item, index) => ({
        ...item,
        sort: index + 1,
      }))
      onDragChange(newData)
    }
  }

  return (
    <SortTable
      dataSource={dataSource}
      columns={columns}
      rowKey="sort"
      sortableItems={dataSource.map((item) => item.sort as number)}
      style={{ minWidth: 800, maxWidth: 1200 }}
      onRow={() => ({ style: { color: color.gray[1].default } })}
      dndContextProps={{ onDragEnd }}
    />
  )
}

export default SortStructuresTable
