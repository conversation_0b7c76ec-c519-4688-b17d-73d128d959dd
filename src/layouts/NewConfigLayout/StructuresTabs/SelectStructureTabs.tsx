import { useState, useRef } from 'react'

import {
  Checkbox, Divider, Flex, Input, type TabsProps,
} from 'antd'
import type { CheckboxChangeEvent } from 'antd/es/checkbox'

import { EyeIcon, EyeOffIcon, SearchIcon } from 'src/assets/icons'
import MultipleTagSelect from 'src/components/Form/MultipleTagSelect'
import SelectTagsContent from 'src/components/SelectTagsContent'
import SubCardTabs from 'src/components/Tabs/SubCardTabs'
import i18n from 'src/i18n'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { updateDetail, updateStructure } from 'src/store/reducers/detailSlice'
import { color } from 'src/utils/variables'

type SubTabItems = 'all' | 'checked'

function SelectStructureTabs() {
  // ref
  const cascaderRef = useRef<HTMLDivElement | null>(null)
  // state
  const [selectedTabItem, setSelectedTabItem] = useState<SubTabItems>('all')
  const [seeColor, setSeeColor] = useState<boolean>(true)
  const [searchText, setSearchText] = useState<string>('')
  const [filterList, setFilterList] = useState<string[]>([])

  // redux
  const dispatch = useAppDispatch()
  const { structureConfig, categoryType } = useAppSelector((state) => state.configReducer)
  const structures = useAppSelector((state) => state.detailReducer.detail.structures)

  const checkedStructures = structures.map((item) => item.id)
  const categoryOptions = categoryType?.map((item) => ({ title: item, key: item, value: item }))

  // data source
  const escapeRegExp = (string: string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  const searchTags = (value: string) => new RegExp(escapeRegExp(searchText), 'i').test(value)
  const filterTagList = (category: string[]) => (
    filterList.length === 0
    || category.some((value) => filterList.includes(value))
  )

  const allDataSource = structureConfig
    .filter(
      ({ efai_structure_name, customized_name, category }) => (
        searchTags(customized_name || efai_structure_name) && filterTagList(category)
      ),
    )
    .map((item) => ({ ...item, checked: checkedStructures.includes(item.id) }))

  const selectedDataSource = structureConfig
    .map((item) => ({ ...item, checked: checkedStructures.includes(item.id) }))
    .filter(({ checked }) => checked)

  // checkbox state
  const isAllChecked = allDataSource.every(({ checked }) => checked)
  const isIndeterminateChecked = allDataSource.some(({ checked }) => checked) && !isAllChecked

  // function
  const handleCheckSingle = (structure: StructureConfigType) => dispatch(updateStructure({ ...structure }))

  const seeColorChange = (e: CheckboxChangeEvent) => setSeeColor(e.target.checked)

  const handleCheckAll = (e: CheckboxChangeEvent) => {
    const { checked } = e.target

    const dataSourceIdSet = new Set(allDataSource.map(({ id }) => id))
    const structureIds = new Set(structures.map(({ id }) => id))

    const updatedStructures = checked
      ? [...new Set([...dataSourceIdSet, ...structureIds])].map((id) => ({ id, sort: null }))
      : structures.filter(({ id }) => !dataSourceIdSet.has(id))

    dispatch(updateDetail({ structures: updatedStructures }))
  }

  const tabItems: TabsProps['items'] = [
    {
      key: 'all',
      label: <div>{i18n.t('buttons.all_structure')}</div>,
      children: (
        <SelectTagsContent
          dataSource={allDataSource}
          structuresTagProps={{
            onChange: handleCheckSingle,
            className: 'cancel-hover',
            seeColor,
          }}
        >
          <Flex
            component="nav"
            ref={cascaderRef}
            justify="space-between"
            align="center"
            style={{
              padding: '1rem 1.25rem',
            }}
          >
            <Flex
              component="ul"
              gap={20}
              style={{
                margin: 0,
                padding: 0,
                height: 28,
                listStyleType: 'none',
              }}
            >
              <li>
                <Checkbox
                  onChange={seeColorChange}
                  checked={seeColor}
                  className="no-checkbox-border basic-shadow"
                  style={{
                    height: '100%',
                  }}
                >
                  <Flex align="center" style={{ lineHeight: '28px' }}>
                    {seeColor ? <EyeIcon /> : <EyeOffIcon />}
                    {i18n.t('checkboxes.color_code')}
                  </Flex>
                </Checkbox>
              </li>
              <li>
                <MultipleTagSelect options={categoryOptions} onChange={(v) => setFilterList(v)} />
              </li>
              <li>
                <Input
                  size="small"
                  placeholder={i18n.t('form_placeholders.search_structure')}
                  onChange={(e) => { setSearchText(e.target.value) }}
                  suffix={<SearchIcon />}
                  className="placeholder-gray-1"
                  style={{
                    display: 'flex',
                    height: 28,
                    paddingInline: 12,
                  }}
                />
              </li>
            </Flex>

            <Checkbox
              onChange={handleCheckAll}
              checked={isAllChecked}
              indeterminate={isIndeterminateChecked}
            >
              {i18n.t('checkboxes.select_all')}
            </Checkbox>
          </Flex>
          <Divider style={{ margin: 0, borderColor: color.gray[4], borderWidth: 2 }} />
        </SelectTagsContent>
      ),
    }, {
      key: 'checked',
      label: (
        <Flex align="center" gap={8}>
          {i18n.t('buttons.selected')}
          <span className="check-count-ball">{checkedStructures.length}</span>
        </Flex>
      ),
      children: (
        <SelectTagsContent
          dataSource={selectedDataSource}
          structuresTagProps={{ closeIcon: false, seeColor: true }}
        />
      ),
    },
  ]

  return (
    <SubCardTabs
      type="card"
      activeKey={selectedTabItem}
      items={tabItems}
      onChange={(value) => setSelectedTabItem(value as SubTabItems)}
    />
  )
}

export default SelectStructureTabs
