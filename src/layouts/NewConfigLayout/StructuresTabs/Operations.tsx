/* eslint-disable jsx-a11y/label-has-associated-control */
import { useState } from 'react'

import {
  Col, Flex, Input, Row, Select,
  type SelectProps,
} from 'antd'
import { isNumber } from 'lodash-es'

import {
  OperationsEmpty,
  OperationsCombine,
  OperationsMargin,
  OperationsCrop,
  OperationsOverlay,
} from 'src/assets/icons'
import ButtonGroup from 'src/components/Button/ButtonGroup'
import DebouncedWithHOC from 'src/components/DebouncedWithHOC'
import SegmentedRadioGroup from 'src/components/Form/SegmentedRadioGroup'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'
import { marginValueCheck } from 'src/utils/verify'

type ChangeHandlers = (data: CustomizedStructuresType) => void
type Props = {
  data: CustomizedStructuresType
  onChange: ChangeHandlers
  validStructure?: number[]
  options?: SelectProps['options']
}

const { marginValueLimit } = window.config.env.configPage

const marginList: Array<MarginValueKey> = ['right', 'anterior', 'inferior', 'left', 'posterior', 'superior']

const getButtonGroup = (data: CustomizedStructuresType, onChange: ChangeHandlers) => [
  {
    key: 'EMPTY_STRUCTURE',
    tooltip: i18n.t('tooltips.operations.empty_structure'),
    icon: <OperationsEmpty />,
    onClick() {
      onChange({
        ...data,
        mode: 'EMPTY_STRUCTURE',
        structure_operations: [],
        margin_value: null,
      })
    },
  },
  {
    key: 'OUTER',
    tooltip: i18n.t('tooltips.operations.outer'),
    icon: <OperationsMargin />,
    onClick() {
      onChange({
        ...data,
        mode: 'OUTER',
        structure_operations: [],
        margin_value: {
          anterior: null,
          posterior: null,
          left: null,
          right: null,
          superior: null,
          inferior: null,
        },
      })
    },
  },
  {
    key: 'COMBINE',
    tooltip: i18n.t('tooltips.operations.combine'),
    icon: <OperationsCombine />,
    onClick() {
      onChange({
        ...data,
        structure_operations: [],
        mode: 'COMBINE',
        margin_value: null,
      })
    },
  },
  {
    key: 'CROP',
    tooltip: i18n.t('tooltips.operations.crop'),
    icon: <OperationsCrop />,
    onClick() {
      onChange({
        ...data,
        structure_operations: [],
        mode: 'CROP',
        margin_value: null,
      })
    },
  },
  {
    key: 'OVERLAP',
    tooltip: i18n.t('tooltips.operations.overlap'),
    icon: <OperationsOverlay />,
    onClick() {
      onChange({
        ...data,
        structure_operations: [],
        mode: 'OVERLAP',
        margin_value: null,
      })
    },
  },
]

const selectFirstText = {
  COMBINE: 'Combine',
  CROP: 'Crop from',
  OVERLAP: 'Overlapping from',
}

const selectCenterText = {
  COMBINE: 'And',
  CROP: 'Minus',
  OVERLAP: 'And',
}

const selectFlexProps = {
  flex: '0 0 200px',
  vertical: true,
  gap: 4,
}

function Operations({
  data, onChange, validStructure = [], options = [],
}: Props) {
  const firstOperation = data.structure_operations?.[0]
  const secondOperation = data.structure_operations?.[1]

  const [symRadio, setSymRadio] = useState<'symmetric' | 'asymmetric'>(data?.margin_symmetric
    ? 'symmetric'
    : 'asymmetric')

  const selectProps: SelectProps = {
    style: { width: '100%' },
    placeholder: i18n.t('form_placeholders.select_structure'),
  }
  const firstOption = options.map((item) => ({
    ...item,
    disabled: item.value === secondOperation,
  }))
  const secondOption = options.map((item) => ({
    ...item,
    disabled: item.value === firstOperation,
  }))

  const handleChangeMargin = (label: MarginValueKey, value: number | null) => {
    if (!['OUTER', 'INNER'].includes(data.mode)) return
    const marginValue = symRadio === 'symmetric'
      ? {
        anterior: value,
        posterior: value,
        left: value,
        right: value,
        superior: value,
        inferior: value,
      }
      : { ...data.margin_value!, [label]: value }

    onChange({
      ...data,
      mode: data.mode as OperationsMarginMode,
      margin_value: marginValue as OperationsMargin['margin_value'],
    })
  }

  const handleChangeSymRadio = (value: 'symmetric' | 'asymmetric') => {
    setSymRadio(value)
    if (value === 'asymmetric') {
      onChange({
        ...data,
        margin_symmetric: false,
      })
      return
    }
    const marginValue = {
      right: data.margin_value?.right,
      anterior: data.margin_value?.right,
      posterior: data.margin_value?.right,
      left: data.margin_value?.right,
      superior: data.margin_value?.right,
      inferior: data.margin_value?.right,
    } as OperationsMargin['margin_value']

    onChange({
      ...data,
      mode: data.mode as OperationsMarginMode,
      margin_symmetric: value === 'symmetric',
      margin_value: marginValue,
    })
  }

  switch (data.mode) {
    case 'EMPTY_STRUCTURE':
      return (
        <p style={{
          margin: 0,
          padding: '1rem 0',
          lineHeight: 1.5,
          color: color.gray[1].default,
          fontSize: 14,
        }}
        >
          This structure is set as an empty structure.
        </p>
      )
    case 'COMBINE':
    case 'CROP':
    case 'OVERLAP':
      return (
        <Flex gap={16} align="center" flex="1 0 550px" style={{ minWidth: 480 }}>
          <Flex {...selectFlexProps}>
            <label htmlFor={`${data.id}-first`} style={{ fontSize: 14 }}>
              {selectFirstText[data.mode]}
            </label>
            <Select
              {...selectProps}
              id={`${data.id}-first`}
              value={validStructure.includes(firstOperation ?? -1) ? firstOperation : null}
              options={firstOption}
              onChange={(value) => {
                onChange({
                  ...data,
                  structure_operations: [value, secondOperation],
                })
              }}
            />
          </Flex>
          <p>{selectCenterText[data.mode]}</p>
          <Flex {...selectFlexProps}>
            <label htmlFor={`${data.id}-second`} style={{ fontSize: 14 }}>
              Second Structure
            </label>
            <Select
              {...selectProps}
              id={`${data.id}-second`}
              value={validStructure.includes(secondOperation ?? -1) ? secondOperation : null}
              options={secondOption}
              onChange={(value) => {
                onChange({
                  ...data,
                  structure_operations: [firstOperation, value],
                })
              }}
            />
          </Flex>
        </Flex>
      )
    case 'OUTER':
    case 'INNER':
      return (
        <Flex gap={16} align="center">
          <Flex {...selectFlexProps}>
            <label htmlFor={`${data.id}-first-margin`} style={{ fontSize: 14 }}>Margin on</label>
            <Select
              {...selectProps}
              id={`${data.id}-first-margin`}
              value={validStructure.includes(firstOperation ?? -1) ? firstOperation : null}
              options={options}
              onChange={(value) => {
                onChange({
                  ...data,
                  structure_operations: [value],
                })
              }}
            />
          </Flex>
          <Flex vertical gap={4} style={{ minWidth: 160 }}>
            <SegmentedRadioGroup
              value={symRadio}
              onChange={(e) => handleChangeSymRadio(e.target.value)}
              options={[
                { label: 'SYM', value: 'symmetric' },
                { label: 'ASYM', value: 'asymmetric' }]}
            />
            <SegmentedRadioGroup
              value={data.mode}
              onChange={(e) => { onChange({ ...data, mode: e.target.value as OperationsMarginMode }) }}
              options={[
                { label: 'Outer', value: 'OUTER' },
                { label: 'Inner', value: 'INNER' }]}
            />
          </Flex>
          <Row wrap gutter={[16, 4]} style={{ width: 460 }}>
            {marginList.map((item) => (
              <Col key={item} span={8}>
                <Flex gap={4} align="center" justify="space-between">
                  <label htmlFor={item}>{i18n.t(`label.margin_value.${item}`)}</label>
                  <DebouncedWithHOC
                    value={data.margin_value?.[item]}
                    onChange={(value) => {
                      const number = Number(value)
                      const check = /^(0|[1-9]\d*)?$/.test(value)
                      handleChangeMargin(item, isNumber(Number(value)) && check ? number : null)
                    }}
                  >
                    <Input
                      status={marginValueCheck(data.margin_value?.[item]) ? 'error' : ''}
                      placeholder="mm"
                      maxLength={String(marginValueLimit).length}
                      style={{ minWidth: 60, maxWidth: 60 }}
                      disabled={symRadio === 'symmetric' && item !== 'right'}
                    />
                  </DebouncedWithHOC>

                </Flex>
              </Col>
            ))}
          </Row>
        </Flex>
      )
    default:
      return (
        <ButtonGroup
          group={getButtonGroup(data, onChange)}
          type="default"
          size="large"
          style={{
            width: 74, height: 56, fontSize: 28,
          }}
        />
      )
  }
}

export default Operations
export { getButtonGroup }
