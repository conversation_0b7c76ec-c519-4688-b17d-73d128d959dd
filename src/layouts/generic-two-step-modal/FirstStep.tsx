import { Form, Input } from 'antd'

import { requiredRules } from 'src/utils/verify'

import { useTwoStepModal } from './context'

function FirstStep() {
  const { state, config } = useTwoStepModal()

  return (
    <Form.Item
      label={(
        <p style={{ textAlign: 'center', fontWeight: 700 }}>
          {config.firstStep.label}
        </p>
      )}
      name="create-name"
      htmlFor="create-name"
      layout="vertical"
      wrapperCol={{ span: 24 }}
      validateStatus={state.isVerify.check ? 'error' : undefined}
      help={state.isVerify.text}
      rules={config.firstStep.validationRules || [requiredRules()]}
      style={{ width: '35%', minWidth: 300, textAlign: 'center' }}
    >
      <Input
        id="create-name"
        placeholder={config.firstStep.placeholder}
        style={{ textAlign: 'center' }}
        maxLength={config.firstStep.maxLength || 16}
      />
    </Form.Item>
  )
}

export default FirstStep
