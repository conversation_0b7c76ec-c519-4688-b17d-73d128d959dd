import { useEffect } from 'react'

import {
  Button, ConfigProvider, Form, Modal,
} from 'antd'

import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

import { TwoStepModalProvider, useTwoStepModal, twoStepModalActions } from './context'
import FirstStep from './FirstStep'
import SecondStep from './SecondStep'
import { GenericTwoStepModalProps, WithId } from './types'

import 'src/styles/components/modal.css'

type OmitKeys = 'firstStepConfig' | 'secondStepConfig' | 'onFirstStepSubmit' | 'onSecondStepSubmit' | 'onError'

type OmitGenericTwoStepModalProps<T extends WithId = WithId> = Omit<GenericTwoStepModalProps<T>, OmitKeys>

interface TwoStepModalContentProps<T extends WithId = WithId> extends OmitGenericTwoStepModalProps<T> { }

const formProps = {
  colon: false,
  layout: 'vertical' as const,
  requiredMark: false,
}

const footerButtonProps = {
  variant: 'outlined' as const,
  style: {
    width: '6.25rem',
    height: '1.75rem',
  },
}

// Internal component that uses the context
function TwoStepModalContent<T extends WithId = WithId>({ ...props }: TwoStepModalContentProps<T>) {
  const {
    state, dispatch, handlers, config: { secondStep: { errorMessage = '' } },
  } = useTwoStepModal()

  // forms
  const [firstStepForm] = Form.useForm()
  const [secondStepForm] = Form.useForm()

  // modal
  const leaveModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.confirm'),
      okText: i18n.t('buttons.leave'),
      onOk: async () => {
        props.dismiss()
        dispatch(twoStepModalActions.resetState())
        firstStepForm.resetFields()
        secondStepForm.resetFields()
      },
      className: 'confirm-modal',
      centered: true,
      okButtonProps: footerButtonProps,
      cancelButtonProps: {
        ...footerButtonProps,
        className: 'btn gray outline',
      },
    },
  })

  const requiredModal = useAntModal({
    modalProps: {
      centered: true,
      className: 'confirm-modal',
      okText: i18n.t('buttons.ok'),
      footer: null,
      styles: {
        body: { padding: '7.5rem 2.5rem' },
      },
    },
  })

  const handleCancel = () => {
    if (state.currentStep === 1) {
      props.dismiss()
      dispatch(twoStepModalActions.setCurrentStep(1))
    } else {
      leaveModal.trigger()
      dispatch(twoStepModalActions.setSelectedId(''))
    }
    dispatch(twoStepModalActions.setVerify({ check: false, text: '' }))
    firstStepForm.resetFields()
    secondStepForm.resetFields()
  }

  const handleSecondPage = async () => {
    await handlers.onSecondStepSubmit(state.selectedId, state.inputValue!)
  }

  const handleFirstPageError = (e: unknown) => {
    if (typeof e === 'object' && e !== null) {
      const text: string = handlers.onError ? handlers.onError(e) : 'An error occurred'
      dispatch(twoStepModalActions.setVerify({ check: true, text }))
    }
  }

  const handleFirstPage = async () => {
    try {
      const formValue = firstStepForm.getFieldsValue()['create-name']
      await handlers.onFirstStepSubmit(formValue)
      dispatch(twoStepModalActions.setInputValue(formValue))
      dispatch(twoStepModalActions.setCurrentStep(2))
      dispatch(twoStepModalActions.setVerify({ check: false, text: '' }))
    } catch (e) {
      handleFirstPageError(e)
    }
  }

  const handleError = () => {
    if (state.currentStep === 2) {
      dispatch(twoStepModalActions.setErrorText(errorMessage))
      requiredModal.trigger()
    }
  }

  const handleFinish = async () => {
    try {
      if (state.currentStep === 1) {
        await firstStepForm.validateFields()
        await handleFirstPage()
      } else {
        await secondStepForm.validateFields()
        await handleSecondPage()
      }
    } catch (e) {
      handleError()
    }
  }

  useEffect(() => {
    if (!props.modalProps.open) {
      return () => {
        firstStepForm.resetFields()
        secondStepForm.resetFields()
        dispatch(twoStepModalActions.resetState())
      }
    }
    return () => { }
  }, [props.modalProps.open])

  return (
    <>
      <ConfigProvider theme={{
        components: {
          Table: {
            borderColor: 'rgba(192, 192, 192, 0.30)',
            headerBg: color.gray[3],
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[1].default,
            rowSelectedHoverBg: color.gray[1].default,
            cellPaddingBlock: 8,
            cellPaddingInline: 16,
          },
        },
      }}
      >
        <Modal
          {...props.modalProps}
          onCancel={handleCancel}
          footer={[
            <Button
              htmlType="button"
              onClick={handleCancel}
              key="cancel"
              className="btn gray outline"
              {...footerButtonProps}
            >
              {i18n.t('buttons.cancel')}
            </Button>,
            <Button
              htmlType="submit"
              onClick={handleFinish}
              key="next"
              color="primary"
              {...footerButtonProps}
            >
              {i18n.t('buttons.next')}
            </Button>,
          ]}
        >
          <Form.Provider
            onFormFinish={(name) => {
              if (name === 'first-step-form') {
                handleFirstPage()
              } else if (name === 'second-step-form') {
                handleSecondPage()
              }
            }}
          >
            {state.currentStep === 1 && (
              <Form
                form={firstStepForm}
                name="first-step-form"
                onFinish={handleFirstPage}
                {...formProps}
              >
                <FirstStep />
              </Form>
            )}

            {state.currentStep === 2 && (
              <Form
                form={secondStepForm}
                name="second-step-form"
                onFinish={handleSecondPage}
                {...formProps}
              >
                <SecondStep />
              </Form>
            )}
          </Form.Provider>
        </Modal>
      </ConfigProvider>

      {/* leave modal */}
      <Modal {...leaveModal.modalProps}>
        {i18n.t('modal_contents.leave_page_confirmation')}
      </Modal>

      {/* required modal */}
      <Modal {...requiredModal.modalProps}>
        {state.errorText}
      </Modal>
    </>
  )
}

// Main component that provides context
function GenericTwoStepModal<T extends WithId = WithId>({
  firstStepConfig,
  secondStepConfig,
  onFirstStepSubmit,
  onSecondStepSubmit,
  onError,
  ...props
}: GenericTwoStepModalProps<T>) {
  return (
    <TwoStepModalProvider<T>
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={onFirstStepSubmit}
      onSecondStepSubmit={onSecondStepSubmit}
      onError={onError}
    >
      <TwoStepModalContent<T> {...props} />
    </TwoStepModalProvider>
  )
}

export default GenericTwoStepModal
export { FirstStep, SecondStep }
export type {
  GenericTwoStepModalProps, FirstStepConfig, SecondStepConfig, WithId,
} from './types'
