import { useRef } from 'react'

import { Form, Flex, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table'

import i18n from '@/i18n'
import { requiredRules } from 'src/utils/verify'

import { useTwoStepModal, twoStepModalActions } from './context'
import { WithId } from './types'

function SecondStep() {
  const { state, config, dispatch } = useTwoStepModal()
  const { tableProps: { columns = [], ...tableProps } } = config.secondStep
  // ref
  const radioRefs: { current: { [key: string]: HTMLInputElement | null } } = useRef({})

  // handlers
  const handleClickRow = (option: string | number) => {
    radioRefs.current[option]!.click()
  }

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(twoStepModalActions.setSelectedId(e.target.id))
  }

  const tableColumns: ColumnsType<WithId<object>> = [
    {
      width: '32px',
      render(_, record) {
        return (
          <input
            type="radio"
            id={record.id.toString()}
            name="radio-group"
            style={{ opacity: 0 }}
            checked={record.id === state.selectedId}
            onChange={handleRadioChange}
            value={record.id.toString()}
            ref={(input) => { radioRefs.current[record.id] = input }}
          />
        )
      },
    },
    ...columns,
  ]

  return (
    <Form.Item
      name="radio-group"
      className="radio-group"
      style={{ marginBottom: 0, width: '100%' }}
      rules={[requiredRules()]}
    >
      <main id="radio-group" style={{ height: '100%' }}>
        <Flex vertical justify="center" align="center">
          <div>
            <label htmlFor="create-option" className="radio-button">
              <span>
                <input
                  id="create-option"
                  type="radio"
                  name="radio-group"
                  checked={state.selectedId === 'create-option'}
                  onChange={handleRadioChange}
                  ref={(input) => { radioRefs.current['create-option'] = input }}
                />
                <span />
              </span>
              <span className="radio-button-text">
                {config.secondStep.createNewOptionText}
              </span>
            </label>
          </div>
          <p>{i18n.t('paragraphs.create_new_modal.second')}</p>
          <h3>{config.secondStep.description}</h3>
        </Flex>
        <Table<WithId<object>>
          columns={tableColumns}
          style={{ height: '100%', padding: '2rem 2rem 0' }}
          scroll={{ y: 'calc(100% - 60px)' }}
          pagination={false}
          rowKey={(record) => record.id}
          onRow={((record) => ({ onClick: () => handleClickRow(record.id) }))}
          rowClassName={((record) => (`${state.selectedId}` === `${record.id}`
            ? 'select-table-row'
            : ''))}
          className="setting-table-container create-new-protocol-table"
          sticky
          {...tableProps}
        />
      </main>
    </Form.Item>
  )
}

export default SecondStep
