import { Table } from 'antd'
import { ColumnsType } from 'antd/es/table'

import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { withoutTime } from 'src/utils/helper'

interface Props {
  copyProtocolID: number | string
  setCopyProtocolID: (id: number) => void
  radioRefs: { current: { [key: string]: HTMLInputElement | null } }
}

function SecondStepTable({ copyProtocolID, setCopyProtocolID, radioRefs }: Props) {
  const { protocols } = useAppSelector((state) => state.protocolReducer)
  const handleClickRow = (option: string | number) => radioRefs.current[option]!.click()

  const columns: ColumnsType<ProtocolType> = [
    {
      width: '32px',
      render(_, { id }) {
        return (
          <input
            type="radio"
            id={id.toString()}
            name="radio-group"
            style={{ opacity: 0 }}
            checked={id === copyProtocolID}
            onChange={() => setCopyProtocolID(id)}
            value={id.toString()}
            ref={(input) => { radioRefs.current[id] = input }}
          />
        )
      },
    },
    {
      title: i18n.t('titles.protocol_name'),
      dataIndex: 'protocol_name',
      key: 'protocol_name',
      width: '160px',
    },
    {
      title: i18n.t('titles.last_modified'),
      dataIndex: 'last_mod_time',
      key: 'last_mod_time',
      width: '180px',
      render(_, { last_mod_time }) {
        return withoutTime(last_mod_time)
      },
    },
    {
      title: i18n.t('titles.description'),
      dataIndex: 'description',
      key: 'description',
      width: '300px',
    },
  ]

  return (
    <Table
      dataSource={protocols}
      columns={columns}
      style={{ height: '100%', padding: '2rem 2rem 0' }}
      scroll={{ y: 'calc(100% - 60px)' }}
      pagination={false}
      rowKey={(record) => record.id}
      onRow={((record) => ({ onClick: () => handleClickRow(record.id) }))}
      rowClassName={((record) => (copyProtocolID === record.id ? 'select-table-row' : ''))}
      className="setting-table-container create-new-protocol-table"
      sticky
    />
  )
}

export default SecondStepTable
