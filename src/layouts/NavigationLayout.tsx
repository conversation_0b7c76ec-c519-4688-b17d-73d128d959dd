import { useEffect, useState } from 'react'

import {
  DoubleLeftOutlined, DoubleRightOutlined, InfoCircleOutlined, QuestionCircleOutlined,
} from '@ant-design/icons'
import { Button, Layout, Menu } from 'antd'
import type { MenuProps } from 'antd'
import {
  Outlet, useNavigate, useLocation, Link,
} from 'react-router'

import {
  HistoryIcon, WorklistIcon, SettingIcon, HourglassIcon,
} from 'src/assets/icons'
import AboutModal, { initDataSorce } from 'src/components/Modal/AboutModal'
import useWebSocketManager from 'src/hooks/useWebSocketManager'
import i18n from 'src/i18n'
import { fetchAboutExcel } from 'src/utils/fetchExcelData'

const { Sider } = Layout

type MenuItem = Required<MenuProps>['items'][number]

const isShowManual = window.config.env.manual.isShow

const { autoSeg } = window.config.env.systemTitle

function getSelectedKey(location: string[]) {
  if (location[1] === 'rs') return location[2]
  return location[1] || 'worklist'
}

function NavigationPage() {
  // state
  const [collapsed, setCollapsed] = useState(window.innerWidth < 1200)
  const [isAboutModalOpen, setIsAboutModalOpen] = useState(false)
  const [aboutDataSource, setAboutDataSource] = useState(initDataSorce)

  // WebSocket
  useWebSocketManager()

  // router
  const subMenu = ['protocols', 'structure', 'source', 'destination', 'templates']
  const navigate = useNavigate()
  const location = useLocation().pathname.split('/')
  const selectedKey = getSelectedKey(location)

  const dismissAboutModal = () => setIsAboutModalOpen(false)

  const menuItems: MenuItem[] = [
    {
      key: 'worklist',
      label: i18n.t('titles.task_list'),
      icon: <WorklistIcon />,
      onClick: () => navigate('/'),
    },
    {
      key: 'queue',
      label: i18n.t('titles.rs_queue'),
      icon: <HourglassIcon />,
      onClick: () => navigate('rs/queue'),
    },
    {
      key: 'history',
      label: i18n.t('titles.history'),
      icon: <HistoryIcon />,
      onClick: () => navigate('/history'),
    },
    {
      key: 'sub',
      label: i18n.t('titles.settings'),
      icon: <SettingIcon />,
      children: [
        {
          key: 'protocols',
          label: i18n.t('titles.protocols'),
          onClick: () => navigate('/protocols'),
        },
        {
          key: 'structure',
          label: i18n.t('titles.structures'),
          onClick: () => navigate('/structure'),
        },
        {
          key: 'templates',
          label: i18n.t('titles.rs_templates'),
          onClick: () => navigate('/rs/templates'),
        },
        {
          key: 'source',
          label: i18n.t('titles.source'),
          onClick: () => navigate('/source'),
        },
        {
          key: 'destination',
          label: i18n.t('titles.destination'),
          onClick: () => navigate('/destination'),
        },
      ],
    },
  ]

  const manualItem: MenuItem[] = isShowManual ? [
    {
      key: 'manual',
      label: 'User manual',
      icon: <QuestionCircleOutlined />,
      onClick: () => navigate('/manual'),
    },
  ] : []

  const floorMenuItems: MenuItem[] = [
    ...manualItem,
    {
      key: 'about',
      label: 'About',
      icon: <InfoCircleOutlined />,
      onClick: () => { setIsAboutModalOpen(true) },
    },
  ]

  useEffect(() => {
    fetchAboutExcel().then((res) => setAboutDataSource(res))
  }, [])

  return (
    <Layout hasSider>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        defaultCollapsed={window.innerWidth < 1200}
        collapsedWidth={76}
        width={250}
        className="navigation-sidebar"
      >
        <div style={{
          padding: '1.5rem 1rem',
        }}
        >
          <header className="sider-header">
            <Link to="/">
              <div className={`logo-box ${collapsed ? 'big-logo' : 'default-logo'}`} />
            </Link>
            <p className={`${!collapsed}`}>{autoSeg ?? i18n.t('titles.autoSeg')}</p>
          </header>
          <Menu
            mode="inline"
            inlineIndent={8}
            defaultOpenKeys={
              window.innerWidth < 1200 ? [] : [subMenu.includes(selectedKey) ? 'sub' : '']
            }
            selectedKeys={[selectedKey]}
            items={menuItems}
          />
        </div>
        <Button
          type="text"
          icon={collapsed
            ? <DoubleRightOutlined />
            : <DoubleLeftOutlined />}
          onClick={() => setCollapsed(!collapsed)}
          className="collapsed-btn"
        />
        <Menu
          mode="inline"
          inlineIndent={8}
          selectedKeys={[selectedKey]}
          items={floorMenuItems}
          className="navigation-sidebar-footer"
        />
      </Sider>
      {/* main */}
      <Layout><Outlet /></Layout>
      <AboutModal
        open={isAboutModalOpen}
        onOk={dismissAboutModal}
        onCancel={dismissAboutModal}
        dataSource={aboutDataSource}
      />
    </Layout>
  )
}

export default NavigationPage
