import { ConfigProvider, ConfigProviderProps } from 'antd'

import { color } from '@/utils/variables'

interface Props extends ConfigProviderProps {
  children: React.ReactNode
}

function SubTableLayout({
  children,
  ...props
}: Props) {
  return (
    <ConfigProvider {...props}>
      <section
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          padding: '.75rem 1rem',
          background: color.gray[3],
        }}
      >
        {children}
      </section>
    </ConfigProvider>
  )
}

export default SubTableLayout
