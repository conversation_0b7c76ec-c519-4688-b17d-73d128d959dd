import type { ReactNode } from 'react'

import { Col, Row } from 'antd'
import type { FormInstance } from 'antd/es/form'
import { Content } from 'antd/es/layout/layout'

import { Head } from 'src/components/Head'
import { SearchList } from 'src/components/List'
import i18n from 'src/i18n'

import 'src/styles/components/tableCascader.css'
import WorklistGroupTable from '../components/Table/WorklistGroupTable'

interface IndexTableLayoutProps {
  isHistoryPage?: boolean
  form: FormInstance
  pageTitle: string
  searchOptions: OptionType[]
  onSearch: (page?: number, sorter?: any) => void
  tableController: any
  children: ReactNode
}

function WorklistLayout({
  isHistoryPage = false,
  pageTitle,
  searchOptions,
  form,
  onSearch,
  tableController,
  children,
}: IndexTableLayoutProps) {
  return (
    <>
      <Head>{i18n.t(pageTitle)}</Head>
      <Content
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          padding: '1rem',
          gap: '1rem',
        }}
      >
        <Row>
          <SearchList
            form={form}
            optionList={searchOptions}
            handleSearch={onSearch}
            datePicker={isHistoryPage}
          />
        </Row>
        <Row style={{ height: '100%', overflow: 'auto' }}>
          <Col span={8}>
            <WorklistGroupTable {...tableController} />
          </Col>
          <Col span={16} style={{ height: '100%' }}>
            {children}
          </Col>
        </Row>
      </Content>
    </>
  )
}

export default WorklistLayout
