import { useState } from 'react'

import {
  Form, Input, Table, Radio, Button,
  Modal,
} from 'antd'
import type { ColumnsType } from 'antd/es/table/interface'

import { color } from '@/utils/variables'
import { XCircleIcon } from 'src/assets/icons'
import { TableInput } from 'src/components/Form'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { checkDicomTagConform } from 'src/utils/helper'
import { requiredRules, dicomTagRules, duplicatedRules } from 'src/utils/verify'

import 'src/styles/components/ListLayout.css'

type RadioFormat = 'exact' | 'regex'

interface Props {
  onAdd?: (data: StudyInformationType) => void
  onDelete?: (data: StudyInformationType) => void
  onChange?: (id: number, fieldName: string, data: StudyInformationType) => void
  data?: StudyInformationType[]
}
const fn = () => { }
function StudyInfoTable({
  onAdd = fn, onDelete = fn, onChange = fn, data = [],
}: Props) {
  const [form] = Form.useForm()
  const [radioFormat, setRadioFormat] = useState<RadioFormat>('exact')
  const deleteModal = useAntModal<StudyInformationType>({
    modalProps: {
      title: i18n.t('modal_titles.delete_confirmation'),
      onOk(value) {
        onDelete(value)
      },
      okText: i18n.t('buttons.delete'),
      className: 'confirm-modal',
      render: (record) => i18n.t(
        'modal_contents.delete_confirmation',
        { item: `"${record?.dicom_tag}"`, joinArrays: ' ' },
      ),
      destroyOnClose: true,
    },
  })

  const onFinish = async () => {
    try {
      await form.validateFields()
      const { dicom_tag: dicomTag, format, value } = form.getFieldsValue()
      onAdd({ dicom_tag: dicomTag, format, value: value || '' })
      form.resetFields()
    } catch (e) {
      // return
    }
  }

  const columns: ColumnsType<StudyInformationType> = [
    {
      title: (
        <Form.Item
          label={i18n.t('titles.dicom_tag')}
          name="dicom_tag"
          labelCol={{ span: 24 }}
          rules={[
            requiredRules('dicom_tag'),
            dicomTagRules(),
            duplicatedRules('dicom_tag', data),
          ]}
        >
          <Input
            maxLength={11}
            placeholder={
              i18n.t('form_placeholders.enter_variable', {
                variable: i18n.t('titles.dicom_tag'),
                joinArrays: ' ',
              })
            }
          />
        </Form.Item>
      ),
      dataIndex: 'dicom_tag',
      key: 'dicom_tag',
      width: 300,
      render: (_, { id, dicom_tag }) => {
        const duplication = data.some(
          (item) => item.id !== id && item.dicom_tag && item.dicom_tag === dicom_tag,
        )
        const errorText = dicom_tag && checkDicomTagConform(dicom_tag)
          ? i18n.t('form_rules.dicom_tag_constraint') : undefined
        return (
          <TableInput
            id={id}
            name="dicom_tag"
            value={dicom_tag}
            required
            duplication={duplication}
            errorText={errorText}
            handleChange={onChange}
          />
        )
      },
    },
    {
      title: (
        <Form.Item
          label={i18n.t('titles.format')}
          name="format"
          labelCol={{ span: 24 }}
          initialValue="exact"
        >
          <Radio.Group
            className="study-info-radio"
            onChange={({ target: { value } }) => setRadioFormat(value)}
          >
            <Radio.Button value="exact">{i18n.t('radios.exact')}</Radio.Button>
            <Radio.Button value="regex">{i18n.t('radios.regex')}</Radio.Button>
          </Radio.Group>
        </Form.Item>
      ),
      dataIndex: 'format',
      key: 'format',
      width: 200,
      render: (_, { id, format }) => {
        return (
          <Radio.Group
            key={id}
            className="study-info-radio"
            value={format}
            onChange={(e) => {
              if (id !== undefined) {
                onChange(id, 'format', e.target.value)
              }
            }}
          >
            <Radio.Button value="exact">{i18n.t('radios.exact')}</Radio.Button>
            <Radio.Button value="regex">{i18n.t('radios.regex')}</Radio.Button>
          </Radio.Group>
        )
      },
    },
    {
      title: (
        <Form.Item
          label={i18n.t('titles.value')}
          name="value"
          labelCol={{ span: 24 }}
        >
          <Input
            placeholder={
              i18n.t('form_placeholders.enter_variable', {
                variable: i18n.t(`form_placeholders.${radioFormat}`),
                joinArrays: ' ',
              })
            }
          />
        </Form.Item>
      ),
      dataIndex: 'value',
      key: 'value',
      render: (_, { id, format, value }) => (
        <TableInput
          id={id}
          name="value"
          value={value}
          handleChange={onChange}
          customizePlaceholder={i18n.t('form_placeholders.enter_variable', {
            variable: i18n.t(`form_placeholders.${format}`),
            joinArrays: ' ',
          })}
        />
      ),
    },
    {
      title: (
        <Button
          htmlType="submit"
          color="primary"
          variant="outlined"
          className="outline"
          style={{ marginTop: 40, height: '32px', lineHeight: 1 }}
        >
          {i18n.t('buttons.add')}
        </Button>
      ),
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          icon={<XCircleIcon style={{ color: color.gray[0].default }} />}
          onClick={() => deleteModal.trigger(record)}
        />
      ),
    },
  ]

  return (
    <>
      <Form form={form} name="studyInformation" onFinish={onFinish}>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          className="add-newRow-table"
          style={{ maxWidth: '990px' }}
        />
      </Form>
      <Modal {...deleteModal.modalProps} />
    </>
  )
}
export default StudyInfoTable
