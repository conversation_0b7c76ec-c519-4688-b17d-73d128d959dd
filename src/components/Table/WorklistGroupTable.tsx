import { ConfigProvider, Table, TablePaginationConfig } from 'antd'
import type { ColumnsType, TableCurrentDataSource } from 'antd/es/table/interface'

import { LoadingIcon2 } from 'src/assets/icons'
import { color } from 'src/utils/variables'

export interface WorklistGroupTableProps<T, K extends keyof T> {
  columns: ColumnsType<T>
  data: T[]
  pagination: TablePaginationConfig
  rowFocus: string | number | null
  rowFocusKey?: K
  isLoading: boolean
  handleSetRowFocus: (record: T[K]) => void
  handleSortChange: (orderKey: string, ascend: boolean) => void
  handlePageChange: (page?: number) => void
}

function WorklistGroupTable<
  T extends { id: string; patient_id: string },
  K extends keyof T,
>({
  columns,
  data,
  pagination,
  rowFocus,
  rowFocusKey = 'id' as K,
  isLoading = false,
  handleSetRowFocus,
  handleSortChange,
  handlePageChange,
}: WorklistGroupTableProps<T, K>) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            borderColor: color.gray[1].variants,
          },
        },
      }}
    >
      <Table<T>
        columns={columns}
        dataSource={data}
        loading={{
          spinning: isLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
        pagination={{
          ...pagination,
          showSizeChanger: false,
          position: ['bottomCenter'],
        }}
        rowKey={(record) => record[rowFocusKey] as React.Key}
        onRow={(record) => ({ onClick: () => handleSetRowFocus(record[rowFocusKey]) })}
        rowClassName={(record) => (rowFocus && record[rowFocusKey] === rowFocus ? 'selected-row' : '')}
        style={{ height: '100%' }}
        className="study-table-container"
        onChange={(
          newPagination,
          _filters,
          sorter,
          extra: TableCurrentDataSource<T>,
        ) => {
          const action = extra?.action

          if (action === 'paginate') {
            handlePageChange(newPagination.current)
          } else if (action === 'sort') {
            const { field, order } = Array.isArray(sorter) ? sorter[0] ?? {} : sorter ?? {}
            if (field) {
              handleSortChange(String(field), order === 'ascend')
            }
          }
        }}
      />
    </ConfigProvider>
  )
}

export default WorklistGroupTable
