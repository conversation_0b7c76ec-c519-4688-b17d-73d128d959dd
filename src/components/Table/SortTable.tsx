import React, { useContext, useMemo } from 'react'

import { MenuOutlined } from '@ant-design/icons'
import { DndContext, type DndContextProps } from '@dnd-kit/core'
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button, Table } from 'antd'
import type { ButtonProps, TableProps } from 'antd'

interface SorTableProps<T> extends TableProps<T> {
  sortableItems: (string | number | {
    id: string | number;
  })[]
  dndContextProps?: DndContextProps
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({})

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

function Row({ style, ...props }: RowProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props['data-row-key'] })

  const rowStyle: React.CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 500 } : {}),
    ...style,
  }

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  )

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={rowStyle} {...attributes} />
    </RowContext.Provider>
  )
}

function SortTable<T>({
  sortableItems, dndContextProps, ...tableProps
}: SorTableProps<T>) {
  return (
    <DndContext modifiers={[restrictToVerticalAxis]} {...dndContextProps}>
      <SortableContext items={sortableItems} strategy={verticalListSortingStrategy}>
        <Table
          rowKey="sort"
          components={{ body: { row: Row } }}
          pagination={false}
          {...tableProps}
        />
      </SortableContext>
    </DndContext>
  )
}

export default SortTable

type DragHandleProps = ButtonProps & {
  listening?: boolean
}

export function DragHandle({ styles, listening = true, ...props }: DragHandleProps) {
  const { setActivatorNodeRef, listeners } = useContext(RowContext)
  return (
    <Button
      type="text"
      size="small"
      icon={<MenuOutlined style={{ fontSize: 16, ...styles?.icon }} />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...props}
      {...(listening ? listeners : {})}
    />
  )
}
