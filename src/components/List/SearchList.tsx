import {
  Button, DatePicker, Form, FormInstance, Input, Select, Space,
} from 'antd'

import { XCircleIcon } from 'src/assets/icons'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

interface Props {
  form: FormInstance<any>
  handleSearch: () => void
  optionList: OptionType[]
  datePicker?: boolean
}

const { RangePicker } = DatePicker

function SearchList({
  form, handleSearch, optionList, datePicker,
}: Props) {
  // Clear single search condition
  const deleteInputValue = (fieldName: string) => {
    form.resetFields([fieldName])
    handleSearch()
  }

  return (
    <Form
      form={form}
      name="search-list"
      className="search-list"
      style={{ padding: '0 2rem', marginTop: '2rem' }}
      onFinish={() => handleSearch()}
    >
      <Space size="large">
        <Form.Item
          name="patientId"
          style={{ minWidth: '160px' }}
        >
          <Input
            placeholder={i18n.t('form_placeholders.search_patient_id')}
            onChange={(e) => e.type === 'click' && deleteInputValue('patientId')}
            allowClear
            style={{ height: '32px' }}
          />
        </Form.Item>
        <Form.Item
          name="studyStatus"
        >
          <Select
            placeholder={i18n.t('form_placeholders.all_study_status')}
            options={optionList}
            style={{ width: '160px', height: '32px', fontSize: '14px' }}
            onClear={() => deleteInputValue('studyStatus')}
            allowClear
          />
        </Form.Item>
        {!!datePicker && (
          <Form.Item
            name="pickDate"
          >
            <RangePicker
              format="YYYY-MM-DD"
              placeholder={[i18n.t('form_placeholders.start_date'), i18n.t('form_placeholders.end_date')]}
              suffixIcon={null}
              size="large"
              style={{ width: '360px', height: '32px' }}
              popupClassName="custom-date-picker"
              onChange={(props) => { if (!props) { deleteInputValue('pickDate') } }}
              allowClear={{
                clearIcon: (
                  <Button
                    htmlType="button"
                    type="link"
                    icon={<XCircleIcon style={{ color: color.gray[0].default }} />}
                    onClick={() => deleteInputValue('pickDate')}
                  />
                ),
              }}
            />
          </Form.Item>
        )}
        <Form.Item>
          <Button
            htmlType="submit"
            type="primary"
            style={{ color: color.gray[4] }}
          >
            {i18n.t('buttons.search')}
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default SearchList
