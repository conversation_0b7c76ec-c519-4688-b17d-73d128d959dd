import { Flex, List } from 'antd'

import { color } from 'src/utils/variables'

type Props = {
  dataSource: AboutList[]
}

function AboutDsecriptionList({ dataSource }: Props) {
  return (
    <List
      dataSource={dataSource}
      renderItem={(item) => (
        <List.Item style={{ minWidth: 'auto', margin: '.5rem 0', padding: 0 }}>
          <List.Item.Meta title={(
            <Flex gap="middle" className="description-list-item">
              <p style={{
                flex: '0 0 10rem',
                textWrap: 'nowrap',
                color: color.gray[1].default,
              }}
              >{item.Title}
              </p>
              <p style={{ flex: 1, color: color.gray[0].default }}>{item.Description}</p>
            </Flex>
          )}
          />
        </List.Item>
      )}
      split={false}
    />
  )
}

export default AboutDsecriptionList
