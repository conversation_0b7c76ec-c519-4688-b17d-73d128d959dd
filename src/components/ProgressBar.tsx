import { WorkStatusEnum } from 'src/utils/enum'
import { formattedStatus } from 'src/utils/helper'
import 'src/styles/components/progressBar.css'

interface Props {
  status: string
  progress?: number
}

function ProgressBar({
  status,
  progress = 0,
}: Props) {
  return (
    <div className="progress-wrapper">
      <div className="progress-title">{formattedStatus(status)}</div>
      <div className="progress-bar">
        <span
          className={`progress-bar-color ${status !== WorkStatusEnum.SUSPENDED && 'primary'}`}
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  )
}

export default ProgressBar
