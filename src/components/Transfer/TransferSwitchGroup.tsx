import React, { useState } from 'react'

import { Flex, Switch } from 'antd'
import { CSSTransition } from 'react-transition-group'

import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

type SwitchGroup = {
  rs_transfer?: boolean
  ct_transfer?: boolean
}

type Props = {
  onChange?: (switchGroup: SwitchGroup) => void
  checked?: SwitchGroup
  style?: React.CSSProperties
  groupText?: {
    rs?: string
    images?: string
  }
  styles?: {
    readonly text: React.CSSProperties
  }
}

function TransferSwitchGroup({
  onChange = () => { },
  checked,
  groupText = {
    rs: `${i18n.t('titles.rs')} ${i18n.t('titles.transfer')}`,
    images: `${i18n.t('titles.image')} ${i18n.t('titles.transfer')}`,
  },
  style,
  styles,
}: Props) {
  const [switchGroup, setSwitchGroup] = useState<SwitchGroup>({
    rs_transfer: false,
    ct_transfer: false,
  })
  const rsChecked = checked ? checked.rs_transfer : switchGroup.rs_transfer
  const ctChecked = checked ? checked.ct_transfer : switchGroup.ct_transfer

  const onChangeSwitchGroup = (group: SwitchGroup) => {
    setSwitchGroup(group)
    onChange(group)
  }

  const handleRsChange = (isCheck: boolean) => {
    if (checked) {
      onChange({ ...checked, rs_transfer: isCheck })
    } else {
      onChangeSwitchGroup({ ...switchGroup, rs_transfer: isCheck })
    }
  }

  const handleCtChange = (isCheck: boolean) => {
    if (checked) {
      onChange({ ...checked, ct_transfer: isCheck })
    } else {
      onChangeSwitchGroup({ ...switchGroup, ct_transfer: isCheck })
    }
  }

  return (
    <Flex
      vertical
      gap={8}
      style={{
        marginLeft: 28, marginTop: 8, textAlign: 'left', ...style,
      }}
    >
      {/* RS */}
      <Flex gap={8} align="center">
        <span style={{ flex: '0 0 124px', ...styles?.text }}>{groupText.rs}</span>
        <span>
          <Switch checked={rsChecked} onChange={handleRsChange} />
        </span>
      </Flex>
      {/* CT */}
      <Flex gap={8} align="center">
        <span style={{ flex: '0 0 124px', ...styles?.text }}>{groupText.images}</span>
        <span>
          <Switch checked={ctChecked} onChange={handleCtChange} />
        </span>
      </Flex>
      <CSSTransition
        in={!rsChecked && !ctChecked}
        timeout={300}
        classNames="slide"
        unmountOnExit
      >
        <p style={{ color: color.primary.default }}>
          {i18n.t('error_contents.destination_switch_error')}
        </p>
      </CSSTransition>
    </Flex>
  )
}

export default TransferSwitchGroup
export type { SwitchGroup }
