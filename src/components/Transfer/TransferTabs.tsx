import { ConfigProvider, Flex } from 'antd'

import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { color } from 'src/utils/variables'

import CheckList, { type CheckListType } from './CheckList'
import 'src/styles/components/transfer.css'

interface Props {
  type: SourceDestinationKey
  checkList: Partial<SourceDestinationType>
  onUpdateRemote: (
    type: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[],
  ) => void
}

const flexLabel = {
  gap: '.5rem',
  align: 'center',
  style: {
    padding: '4px 1rem',
    lineHeight: '32px',
    color: color.primary.default,
    background: color.gray[2],
    borderRadius: '4px 4px 0 0',
    cursor: 'default',
  },
}

function TransferTabs({ type, checkList, onUpdateRemote }: Props) {
  // options data
  const { remoteServerConfig, folderConfig } = useAppSelector((state) => state.configReducer)
  const createOptions = (list: RemoteConfigType[]): CheckListType[] => list
    .filter((item) => item.type.toLocaleLowerCase() === type)
    .map((item) => ({
      id: String(item.id),
      key: item.id,
      label: item.name,
    }))
  const remoteServerOptions: CheckListType[] = createOptions(remoteServerConfig)
  const folderOptions: CheckListType[] = createOptions(folderConfig)

  // checked data
  const remoteServerChecked = checkList[type]?.remote_server ?? []
  const folderChecked = checkList[type]?.folder ?? []

  // update function
  const onUpdateRemoteServer = (checkedState: RemoteType[]) => {
    onUpdateRemote(type, 'remote_server', checkedState)
  }

  const onUpdateFolder = (checkedState: RemoteType[]) => {
    onUpdateRemote(type, 'folder', checkedState)
  }

  return (
    <ConfigProvider theme={{
      components: {
        Collapse: {
          contentBg: color.primary.default,
          headerPadding: 0,
          colorPrimaryBorder: 'transparent',
        },
        List: {
          titleMarginBottom: 8,
          marginXXS: 8,
        },
      },
    }}
    >
      <Flex gap={32} style={{ margin: 0 }}>
        {/* Remote Server */}
        <div style={{ paddingLeft: 0, width: 512 }}>
          <Flex>
            <Flex {...flexLabel}>
              {i18n.t('titles.remote_server')}
              <span className="check-count-ball" style={{ color: '#fff' }}>
                {remoteServerChecked?.length ?? 0}
              </span>
            </Flex>
          </Flex>
          <CheckList
            onChecked={onUpdateRemoteServer}
            dataSource={remoteServerOptions}
            checkedState={remoteServerChecked}
            isSwitch={type === 'destination'}
          />
        </div>

        {/* Folder */}
        <div style={{ paddingRight: 0, width: 512 }}>
          <Flex>
            <Flex {...flexLabel}>
              {i18n.t('titles.folder')}
              <span className="check-count-ball" style={{ color: '#fff' }}>
                {folderChecked?.length ?? 0}
              </span>
            </Flex>
          </Flex>
          <CheckList
            onChecked={onUpdateFolder}
            dataSource={folderOptions}
            checkedState={folderChecked}
            isSwitch={type === 'destination'}
          />
        </div>
      </Flex>
    </ConfigProvider>

  )
}

export default TransferTabs
