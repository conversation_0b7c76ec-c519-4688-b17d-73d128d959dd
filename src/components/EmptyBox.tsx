import React from 'react'

import { Empty, type EmptyProps } from 'antd'

import { EmptyIcon } from 'src/assets/icons'
import { color } from 'src/utils/variables'

type Props = Omit<EmptyProps, 'rootClassName' | 'imageStyle'> & {
  classNames?: {
    readonly icon?: string
  }
}

function EmptyBox({
  className, classNames, styles, style, ...emptyProps
}: Props) {
  return (
    <Empty
      image={(
        <EmptyIcon
          style={{
            fontSize: '48px', ...styles?.image,
          }}
          className={classNames?.icon}
        />
      )}
      className={`flex-col-center  ${className} ${styles?.root} `}
      style={{
        padding: 20,
        color: color.gray[1].default,
        ...style,
      }}
      {...emptyProps}
    />
  )
}

export default EmptyBox
