import { StructuresTagList, StructuresTagProps } from 'src/components/Tag/StructuresTag'
import { color } from 'src/utils/variables'

type CheckedTagsType = StructureConfigType & {
  checked: boolean
}

interface Props {
  dataSource: CheckedTagsType[]
  children?: React.ReactNode
  structuresTagProps?: Partial<Omit<StructuresTagProps, 'tagData'>>
}

function SelectTagsContent({
  dataSource, children, structuresTagProps,
}: Props) {
  return (
    <section style={{ background: color.gray[3] }}>
      {children}
      {/* search bar */}
      <main style={{ minHeight: 300, padding: '0.5rem 1.25rem' }}>
        <StructuresTagList dataSource={dataSource} tagProps={structuresTagProps} />
      </main>
    </section>
  )
}

export default SelectTagsContent
