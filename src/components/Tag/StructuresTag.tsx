import {
  Flex, Tag as AntdTag, ConfigProvider, List, ListProps as AntdListProps,
} from 'antd'

import { color } from 'src/utils/variables'

import ColorLabel from './ColorLabel'

interface TagProps {
  tagData: StructureConfigType
  checked: boolean
  seeColor?: boolean
  onChange?: (structures: StructureConfigType) => void
  className?: string
  closeIcon?: boolean
  classNames?: {
    readonly text?: string
    readonly checked?: string
  }
}

function StructuresTag({
  tagData, checked, seeColor, onChange, className,
  closeIcon = true, classNames,
}: TagProps) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Tag: {
            defaultColor: color.gray[1].default,
            colorTextLightSolid: color.gray[0].default,
            colorBorder: 'transparent',
            colorPrimary: color.gray[5],
            colorPrimaryHover: color.gray[0].alpha,
            colorPrimaryActive: color.gray[0].alpha,
          },
        },
      }}
    >
      <AntdTag.CheckableTag
        checked={checked}
        onChange={() => onChange?.(tagData)}
        className={`structure-tag ${className}`}
        style={{
          width: '100%',
          padding: '4px 8px',
          margin: 0,
          maxWidth: 500,
          minWidth: 420,
          cursor: closeIcon ? 'pointer' : 'default',
          color: color.gray[0].default,
          background: closeIcon ? undefined : color.gray[5],
        }}
      >
        <Flex align="center" justify="space-between" gap={4}>
          <Flex
            component="span"
            align="center"
            gap={12}
            className="user-select-none"
            style={{
              userSelect: 'none',
            }}
          >
            <ColorLabel style={{
              display: seeColor ? 'block' : 'none',
              fontSize: 18,
              color: tagData.color_code,
            }}
            />
            <span className={classNames?.text}>{tagData.customized_name || tagData.efai_structure_name}</span>
            <span
              className={`${closeIcon ? 'structure-tag-icon' : ''} 
                ${checked ? 'tag-checkable' : ''} ${classNames?.checked}`}
            />
          </Flex>
          <Flex gap={4}>
            {tagData?.tag?.map((category) => (
              <AntdTag
                key={category}
                style={{
                  margin: 0,
                  borderRadius: 50,
                  background: color.gray[0].alpha,
                  color: color.gray[1].default,
                }}
              >{category}
              </AntdTag>
            ))}
          </Flex>
        </Flex>
      </AntdTag.CheckableTag>
    </ConfigProvider>
  )
}

type ListProps = AntdListProps<StructureConfigType & { checked: boolean }> & {
  tagProps?: Partial<Omit<TagProps, 'tagData'>>
}

export function StructuresTagList({ dataSource, tagProps: structuresTagProps, ...props }: ListProps) {
  return (
    <List
      grid={{ gutter: 16 }}
      dataSource={dataSource}
      renderItem={(item) => (
        <List.Item styles={{ actions: { width: '100%' } }}>
          <StructuresTag
            tagData={item}
            checked={item.checked}
            seeColor
            {...structuresTagProps}
          />
        </List.Item>
      )}
      {...props}
    />
  )
}

export default StructuresTag
export type { TagProps as StructuresTagProps }
