import React from 'react'

import { ConfigProvider, Tabs, type TabsProps } from 'antd'

import { color } from 'src/utils/variables'

type Props = TabsProps & {}

function ConfigSubTabs({ className, rootClassName, ...tabsProps }: Props) {
  return (
    <ConfigProvider theme={{
      components: {
        Tabs: {
          colorBgContainer: color.primary.default,
          colorPrimaryBorder: color.primary.default,
          colorBorderSecondary: 'transparent',
          cardPadding: '2px 1rem',
          lineHeight: 2,
          margin: 24,
        },
      },
    }}
    >
      <Tabs
        defaultActiveKey="1"
        type="card"
        tabBarGutter={24}
        className={`config-sub-tabs ${rootClassName} ${className}`}
        {...tabsProps}
      />
    </ConfigProvider>
  )
}

export default ConfigSubTabs
