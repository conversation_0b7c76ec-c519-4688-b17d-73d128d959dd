import { CSSProperties } from 'react'

import { Space } from 'antd'

import Button from 'src/components/Button'
import ModalBtn from 'src/components/Modal/ModalBtn'

interface Props {
  items: FooterBtnType[]
  style?: CSSProperties
}

function FooterBtnGroup({
  style, items,
}: Props) {
  const btnStyle = {
    width: '6.25rem',
    lineHeight: 1.15,
    height: '2rem',
  }

  return (
    <Space size={16} style={{ ...style, marginLeft: 'auto' }}>
      {items.map((item) => {
        if (item.type === 'button') {
          return (
            <Button
              key={item.btnName as string}
              className={`${item.btnClass} outline`}
              onClick={item.btnClick}
              style={btnStyle}
              disabled={item.btnDisabled}
            >
              {item.btnName}
            </Button>
          )
        }
        return (
          <ModalBtn
            key={item.btnName as string}
            btnName={item.btnName}
            modalTitle={item.modalTitle as string}
            btnClick={item.btnClick}
            modalClassName={item.modalClassName}
            btnStyle={btnStyle}
            okText={item.okText}
            onOk={item.onOk}
            onCancel={item.onCancel}
            btnDisabled={item.btnDisabled}
            modalFooter={item.modalFooter}
            modalStyle={item.modalStyle}
            modalBodyStyle={item.modalBodyStyle}
            tooltip={item.tooltip}
          >
            {item.modalContent}
          </ModalBtn>
        )
      })}
    </Space>
  )
}

export default FooterBtnGroup
