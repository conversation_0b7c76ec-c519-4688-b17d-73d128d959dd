import React, {
  Ref, useRef, useState,
} from 'react'

import {
  Button, Col, Descriptions, Flex, Input, Modal, Row, Select, Switch,
} from 'antd'
import type { DescriptionsProps, ModalProps } from 'antd'

import { EditIcon } from 'src/assets/icons'
import { StructuresTagList } from 'src/components/Tag/StructuresTag'
import type { ConfigDescriptionItem } from 'src/context/moduleList'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { color } from 'src/utils/variables'
import 'src/styles/components/modal.css'

const menu = [
  'Source & Destination',
  'Structures',
  'Study Information',
  'Add Structures',
]

const defaultInputStyle = {
  width: '100%',
  maxWidth: 350,
}

type DescriptionDataType = DescriptionsProps['items']
interface Props extends ModalProps {
  detail: ProtocolDetailType
  descriptionData: ConfigDescriptionItem[]
  setRouterBlocker: React.Dispatch<React.SetStateAction<boolean>>
}

function CreateNewFinalModal({
  detail,
  setRouterBlocker, descriptionData,
  ...modalprops
}: Props) {
  // ref
  const modalRef = useRef<HTMLElement>(null)
  const headerRef = useRef<HTMLElement>(null)
  // state
  const [btnActive, setBtnActive] = useState<string>('Source & Destination')
  // redux
  const { structureConfig, remoteServerConfig, folderConfig } = useAppSelector((state) => state.configReducer)

  const scrollToSection = (element: string) => {
    const scrollTo = modalRef.current?.querySelector(element)
    if (scrollTo) {
      scrollTo.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handleMenuBtnClick = (id: string) => {
    const newId = id.replace(/[\s&]+/g, '-')
    scrollToSection(`#${newId}`)
    setBtnActive(id)
  }

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    // 使用 event.currentTarget 獲取滾動元素
    const { scrollTop, clientHeight } = event.currentTarget

    const newScrollTop = scrollTop + ((clientHeight / 2) - 32)
    const menuID = (name: string) => modalRef.current?.querySelector(name)?.scrollHeight
    const reutrnHeight = (name: string): number => (modalRef.current as HTMLElement).querySelector(name)!.scrollHeight

    const remoteHeight: number = menuID('#Source-Destination') ? reutrnHeight('#Source-Destination') : 600
    const structuresHeight: number = menuID('#Structures') ? reutrnHeight('#Structures') : 1069
    const studiesHeight: number = menuID('#Study-Information') ? reutrnHeight('#Study-Information') : 1492

    const isInStructuresSection = (remoteHeight + 32 + 48) < newScrollTop
      && newScrollTop <= (remoteHeight + structuresHeight + 32 + 48 * 2)
    const isInStudySection = newScrollTop > (remoteHeight + structuresHeight + 32 + 48 * 2)
      && newScrollTop <= (remoteHeight + structuresHeight + studiesHeight + 32 + 48 * 3)
    const isInAddEmptySection = newScrollTop > (remoteHeight + structuresHeight + studiesHeight + 32 + 48)

    let active: string = ''

    if (isInStructuresSection) {
      active = 'Structures'
    } else if (isInStudySection) {
      active = 'Study Information'
    } else if (isInAddEmptySection) {
      active = 'Add Structures'
    } else {
      active = 'Source & Destination'
    }

    setBtnActive(active)
  }

  // remote
  const createIdSet = (arr: RemoteType[]) => new Set(arr.map((item) => item.id))

  const sourceServerIds = createIdSet(detail.source.remote_server)
  const sourceFolderIds = createIdSet(detail.source.folder)
  const destinationServerIds = createIdSet(detail.destination.remote_server)
  const destinationFolderIds = createIdSet(detail.destination.folder)

  const descriptionItems: DescriptionDataType = descriptionData?.map(({
    label, value, type, props, required,
  }) => {
    let children: React.ReactNode

    switch (type) {
      case 'input':
        children = (
          <Input
            size="small"
            {...props}
            suffix={<EditIcon style={{ fontSize: '1.5rem' }} />}
            style={{ ...defaultInputStyle, ...props?.style }}
          />
        )
        break
      case 'select':
        children = (
          <Select style={{ ...defaultInputStyle, ...props?.style }} {...props} />
        )
        break
      case 'switch':
        children = (
          <Flex align="center" justify="space-between" flex="0 0 96px">
            <span>
              {value && i18n.t(`switch_options.${String(value).toLocaleLowerCase()}`)}
            </span>
            <Switch {...props} />
          </Flex>
        )
        break
      default:
        children = value
        break
    }
    return {
      key: label,
      label: (
        <p style={{ width: 160 }}>
          {required && <span>*</span>} {i18n.t(`titles.${label}`)}:
        </p>),
      children,
    }
  })

  const remoteArr = [
    {
      name: i18n.t('titles.source'),
      remote_server: remoteServerConfig
        .filter((item) => sourceServerIds.has(item.id))
        .map((item) => item.name),
      folder: folderConfig
        .filter((item) => sourceFolderIds.has(item.id))
        .map((item) => item.name),
    },
    {
      name: i18n.t('titles.destination'),
      remote_server: remoteServerConfig
        .filter((item) => destinationServerIds.has(item.id))
        .map((item) => item.name),
      folder: folderConfig
        .filter((item) => destinationFolderIds.has(item.id))
        .map((item) => item.name),
    },
  ]

  return (
    <Modal
      {...modalprops}
      title={i18n.t('modal_titles.new_protocol_name')}
      centered
      okText={i18n.t('buttons.save')}
      className="confirm-create-new-modal"
    >
      <header className="confirm-header" ref={headerRef}>
        <Descriptions
          column={2}
          items={descriptionItems}
          colon={false}
          style={{
            marginBottom: 32,
          }}
        />
      </header>
      <Row className="confirm-body" onScroll={handleScroll}>
        <Col span={6}>
          <menu className="confirm-body-menu">
            {
              menu.map((item) => (
                <Button
                  type="link"
                  key={item}
                  onClick={() => handleMenuBtnClick(item)}
                  style={{
                    display: 'block',
                    marginBottom: '.75rem',
                    height: 'auto',
                    fontWeight: btnActive === item ? 700 : 400,
                    color: btnActive === item ? color.primary.default : color.gray[1].default,
                  }}
                  block
                >
                  <p style={{ whiteSpace: 'break-spaces' }}>{item}</p>
                </Button>
              ))
            }
          </menu>
        </Col>
        <Col span={18} ref={modalRef as Ref<HTMLDivElement>}>
          <main className="confirm-body-content">
            <section className="confirm-body-content-section" id="Source-Destination">
              <h3 className="section-title">{i18n.t('titles.source_destination')}</h3>
              {
                remoteArr.map((item) => (
                  <>
                    <h4>{item.name}</h4>
                    <div className="remote-tag-list">
                      <p>{i18n.t('titles.remote_server')}</p>
                      {item.remote_server.map((i, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <span key={`${i}-remote-server-${index}`}>
                          {i}
                        </span>
                      ))}
                    </div>
                    <div className="remote-tag-list">
                      <p>{i18n.t('titles.folder')}</p>
                      {item.folder.map((i, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <span key={`${i}-folder-${index}`}>
                          {i}
                        </span>
                      ))}
                    </div>
                  </>
                ))
              }
            </section>
            <section className="confirm-body-content-section" id="Structures">
              <h3 className="section-title">{i18n.t('titles.structures')}</h3>
              <div className="section-main" style={{ padding: 0 }}>
                <StructuresTagList
                  dataSource={detail.structures.map((item) => {
                    const findItem = structureConfig.find((i) => i.id === item.id) as StructureConfigType
                    return ({
                      ...findItem,
                      checked: true,
                    })
                  })}
                  tagProps={{
                    closeIcon: false,
                    className: 'tag-checkable',
                    classNames: {
                      checked: 'structure-tag-icon',
                    },
                  }}
                />
              </div>
            </section>
            <section
              className="confirm-body-content-section"
              id="Study-Information"
            >
              <h3 className="section-title">{i18n.t('titles.study_info')}</h3>
              <ul className="studies-list">
                {
                  detail.study_info.map((item) => (
                    <li key={item.dicom_tag} className="studies-list-item">
                      <p>{item.dicom_tag}</p>
                      <p>{i18n.t(`titles.${item.format}`)}</p>
                      <p>{item.value}</p>
                    </li>
                  ))
                }
              </ul>
            </section>
            <section
              className="confirm-body-content-section"
              id="Add-Empty-Structures"
            >
              <h3 className="section-title">{i18n.t('titles.add_structures')}</h3>
              <ul className="studies-list">
                {
                  detail.customized_structures.map((item) => (
                    <li key={item.name} className="studies-list-item">
                      <p>{item.name}</p>
                      <p>{item.type}</p>
                      <p className="color-box">
                        <span style={{ background: `${item.color_code}` }} />
                      </p>
                    </li>
                  ))
                }
              </ul>
            </section>
          </main>
        </Col>
      </Row>
    </Modal>
  )
}
export default CreateNewFinalModal
