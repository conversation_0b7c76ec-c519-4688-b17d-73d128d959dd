import React from 'react'

import {
  Divider, Flex, Modal, ModalProps,
} from 'antd'
import findLastIndex from 'lodash-es/findLastIndex'

import { buildRowData } from 'src/utils/renderUI'

import Button from '../Button'

interface Props extends ModalProps {
  dataSource: AboutDataSource
}

export const initDataSorce: AboutDataSource = {
  container: [],
}

function AboutModal({ dataSource = initDataSorce, onOk, ...props }: Props) {
  const rowData = buildRowData(dataSource.container)
  return (
    <Modal
      title="About"
      className="about-modal-content"
      width={760}
      footer={(
        <Button
          className="outline"
          onClick={onOk}
          style={{ padding: '0 .5rem', width: 100, lineHeight: 1.75 }}
        >Close
        </Button>
      )}
      {...props}
    >

      {rowData.map((item, index) => {
        return (
          <React.Fragment key={`${item.key || index}`}>
            <Flex gap={16} className="description-list-item">
              {React.Children.map(item.children, (child) => (React.isValidElement(child)
                ? React.cloneElement(child, { key: `${child}` })
                : child))}
            </Flex>
            <Divider style={{ margin: '16px 0', display: findLastIndex(rowData) === index ? 'none' : 'block' }} />
          </React.Fragment>
        )
      })}
    </Modal>
  )
}

export default AboutModal
