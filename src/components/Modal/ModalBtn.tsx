import React from 'react'

import { Modal, Tooltip } from 'antd'

import Button from 'src/components/Button'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import 'src/styles/components/modal.css'

interface Props extends ModalBtnType {
  children?: React.ReactNode
}

function ModalBtn({
  btnName = i18n.t('buttons.'),
  btnType,
  modalTitle,
  modalContent,
  children,
  modalStyle,
  modalClassName,
  okText,
  btnClick,
  cancelText,
  onCancel,
  onOk,
  btnStyle,
  btnClass,
  modalBodyStyle,
  width = 520,
  btnDisabled = false,
  modalFooter,
  tooltip,
}: Props) {
  const modal = useAntModal({
    triggerProps: {
      onClick: btnClick,
    },
    modalProps: {
      onOk,
      onCancel,
      cancelText,
      okText,
      title: modalTitle,
      style: modalStyle,
      styles: { body: modalBodyStyle },
      width,
      className: `modal-footer-btn-cursor ${modalClassName}`,
      footer: modalFooter,
      centered: true,
    },
  })

  return (
    <>
      <Tooltip title={tooltip}>
        <Button
          type={btnType}
          onClick={modal.triggerProps.onClick}
          style={btnStyle}
          className={btnClass === 'gray' ? ` ${btnClass}` : `outline ${btnClass}`}
          disabled={btnDisabled}
        >{btnName}
        </Button>
      </Tooltip>
      <Modal {...modal.modalProps}>
        {children || modalContent}
      </Modal>
    </>
  )
}

export default ModalBtn
