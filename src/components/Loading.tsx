import { Modal } from 'antd'

import { LoadingIcon2 } from 'src/assets/icons'

type Props = {
  open?: boolean
}

function Loading({ open = false }: Props) {
  return (
    <Modal
      open={open}
      footer={null}
      closeIcon={false}
      style={{
        backgroundColor: 'var(--color-gray_5)',
      }}
      styles={{
        body: {
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 8,
        },
      }}
      width={150}
      centered
    >
      <LoadingIcon2 className="spin-animation" />
      <p>Loading...</p>
    </Modal>
  )
}

export default Loading
