import React from 'react'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> { }

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, ...props }, ref) => (
    <button
      ref={ref}
      type="button"
      {...props}
      className={`btn ${className}`}
    />
  ),
)

Button.displayName = 'Button'

export default Button
export type { ButtonProps }
