import React from 'react'

import { Button, Flex, Tooltip } from 'antd'
import type { ButtonProps, FlexProps } from 'antd'

type Props = ButtonProps & {
  flexProps?: FlexProps
  group?: Array<ButtonProps & {
    key: React.Key
    tooltip?: string
  }>
}

function ButtonGroup({ group = [], flexProps: flex, ...buttonProps }: Props) {
  return (
    <Flex component="nav" gap={8} {...flex}>
      {group.map(({ key, tooltip, ...button }) => (
        <Tooltip key={key} title={tooltip}>
          <Button
            size="middle"
            {...buttonProps}
            {...button}
            color={buttonProps.color}
          />
        </Tooltip>
      ))}
    </Flex>
  )
}

export default ButtonGroup
