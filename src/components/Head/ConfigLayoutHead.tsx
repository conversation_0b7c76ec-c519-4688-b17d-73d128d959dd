import { To, useNavigate } from 'react-router'

import { LeftIcon } from 'src/assets/icons'

import Head from './Head'
import Button from '../Button'

interface ConfigLayoutHeadProps {
  children: JSX.Element | string
  navigatePage?: To
}

function ConfigLayoutHead({ children, navigatePage }: ConfigLayoutHeadProps) {
  const navigate = useNavigate()

  const backToPage = () => {
    if (navigatePage) {
      return navigate(navigatePage)
    }
    return navigate(-1)
  }

  return (
    <Head>
      <Button onClick={backToPage} className="icon-only">
        <LeftIcon width="24" height="24" />
      </Button>
      {children}
    </Head>
  )
}

export default ConfigLayoutHead
export type { ConfigLayoutHeadProps }
