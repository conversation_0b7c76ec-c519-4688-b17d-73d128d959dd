import { useEffect, useState } from 'react'

import { <PERSON><PERSON>, Drawer } from 'antd'

import { BellIcon, RightIcon, CancelIcon } from 'src/assets/icons'
import Button from 'src/components/Button'
import { Notice } from 'src/components/Modal'
import useAlert from 'src/hooks/useAlert'
import i18n from 'src/i18n'
import { useDeleteNotifyMutation, useGetNotifyMutation, useReGetNotifyMutation } from 'src/services/api'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { clearNotifyQuantity, resetNotifyState, updateNotifyQuantity } from 'src/store/reducers/notifySlice'

function NotifyListDrawer() {
  const [open, setOpen] = useState<boolean>(false)
  const { totalNotify, notifyQuantity, notifyList } = useAppSelector((state) => state.notifyReducer)
  const { message } = useAppSelector((state) => state.websocketReducer)
  const [deleteNotifyMutation] = useDeleteNotifyMutation()
  const [getNotifyMutation] = useGetNotifyMutation()
  const [reGetNotifyMutation] = useReGetNotifyMutation()
  const dispatch = useAppDispatch()

  // hook
  const handleAlert = useAlert()
  const getNotify = async () => {
    try {
      await getNotifyMutation({ quantity: notifyQuantity }).unwrap()
    } catch (e) {
      console.error(e)
    }
  }

  const reGetNotify = async () => {
    try {
      await reGetNotifyMutation({ quantity: notifyQuantity }).unwrap()
    } catch (e) {
      console.error(e)
    }
  }

  const onClose = () => {
    dispatch(clearNotifyQuantity())
    setOpen(false)
  }

  const handleClear = async (notify_id?: number) => {
    try {
      await deleteNotifyMutation({ notify_id }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleShowMore = async () => {
    const quantity = notifyQuantity + 10
    try {
      await getNotifyMutation({ quantity }).unwrap()
      dispatch(updateNotifyQuantity({ quantity }))
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  useEffect(() => {
    getNotify()
    return () => {
      dispatch(resetNotifyState())
    }
  }, [])

  useEffect(() => {
    if (message.content !== 'notify_update') return
    reGetNotify()
  }, [message.timestamp])

  return (
    <>
      <Button
        type="button"
        onClick={() => setOpen(true)}
        className="notice-btn btn-link"
      >
        <Badge count={totalNotify}>
          <BellIcon />
        </Badge>
      </Button>
      <Drawer
        title={(
          <>
            <Badge style={{ color: '#fff' }} count={totalNotify}>
              <BellIcon />
            </Badge>
            <h2>{i18n.t('titles.notice')}</h2>
            <Button
              type="button"
              className="btn-link"
              onClick={() => handleClear()}
              style={{
                background: 'none',
                display: 'flex',
                gap: '.25rem',
                alignItems: 'center',
              }}
            >
              {i18n.t('buttons.clear_all')}
              <CancelIcon />
            </Button>
          </>
        )}
        placement="right"
        onClose={onClose}
        open={open}
        closeIcon={<RightIcon />}
      >
        {notifyList.map((item) => (
          <Notice
            key={item.id}
            id={item.id}
            type={item.type}
            patient_id={item.patient_id}
            msg={item.message}
            time={item.time}
            handleClear={handleClear}
          />
        ))}
        {notifyQuantity < totalNotify ? (
          <div style={{ marginTop: '1rem', textAlign: 'center' }}>
            <Button className="link-btn" onClick={handleShowMore}>
              {i18n.t('buttons.show_more')}
            </Button>
          </div>
        ) : null}
      </Drawer>
    </>
  )
}

export default NotifyListDrawer
