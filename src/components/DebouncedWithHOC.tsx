import React, { cloneElement, useEffect, useState } from 'react'

import { useDebouncedCallback } from 'use-debounce'

interface Props {
  defaultValue?: string | number | null;
  value?: string | number | null;
  delay?: number;
  children: React.ReactElement;
  onChange?: (value: string) => void;
  onChangeKey?: string
}

function DebouncedWithHOC({
  defaultValue, value,
  onChange = () => { },
  delay = 500, children,
  onChangeKey = 'onChange',
}: Props) {
  // internal state for debounced value
  const [innerValue, setInnerValue] = useState(defaultValue)

  // Debounce onChange callback to reduce rapid updates
  const debouncedOnChange = useDebouncedCallback((newVal: string) => {
    onChange(newVal)
  }, delay)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newVal = e.currentTarget.value
    setInnerValue(newVal)
    debouncedOnChange(newVal)
  }

  useEffect(() => {
    setInnerValue(defaultValue)
  }, [defaultValue])

  // If component is controlled, sync innerValue with value prop
  useEffect(() => {
    if (value !== undefined) {
      setInnerValue(value)
    }
  }, [value])

  return cloneElement(children, {
    defaultValue,
    value: innerValue,
    [onChangeKey]: handleChange,
  })
}

export default DebouncedWithHOC
