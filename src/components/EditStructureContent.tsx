import {
  JSXElementConstructor, ReactElement, useRef, useState,
} from 'react'

import {
  Checkbox, Flex, Input,
} from 'antd'
import { CheckboxChangeEvent } from 'antd/es/checkbox'

import {
  EyeIcon, EyeOffIcon, SearchIcon,
} from 'src/assets/icons'
import i18n from 'src/i18n'

import EmptyBox from './EmptyBox'
import MultipleTagSelect from './Form/MultipleTagSelect'
import { StructuresTagList } from './Tag/StructuresTag'

type SelectedRadio = 'all' | 'checked'
interface Props {
  selectedRadio: SelectedRadio
  selectedStructures: number[]
  structureConfig: StructureConfigType[]
  categoryType?: string[]
  handleUpdateStructures: (structures: number[] | number) => void
}

function EditStructureContent({
  selectedRadio, selectedStructures, structureConfig, categoryType,
  handleUpdateStructures,
}: Props) {
  // ref
  const cascaderRef = useRef<HTMLDivElement | null>(null)

  // state
  const [seeColor, setSeeColor] = useState<boolean>(true)
  const [searchText, setSearchText] = useState<string>('')
  const [filterList, setFilterList] = useState<string[]>(['All'])

  const options = categoryType?.map((item) => ({ title: item, key: item, value: item }))

  const updateStructures = (structures: number[]) => {
    handleUpdateStructures(structures)
  }

  const seeColorChange = (e: CheckboxChangeEvent) => {
    setSeeColor(e.target.checked)
  }

  const filterTagList: number[] = structureConfig
    .filter(({ category }) => category.some((value) => filterList.includes(value)))
    .map(({ id }) => id)

  const isFilterMode = filterTagList.length > 0 && !filterList.includes('All')

  const selectedFilterTagList = isFilterMode
    ? selectedStructures.filter((id) => filterTagList.includes(id))
    : selectedStructures

  const totalStructures = isFilterMode
    ? filterTagList
    : structureConfig.map(({ id }) => id)

  const isListChecked = selectedFilterTagList.length === totalStructures.length
  const isIndeterminate = selectedFilterTagList.length > 0 && !isListChecked

  const handleAllSelect = () => {
    updateStructures([...new Set([...selectedStructures, ...totalStructures])])
  }

  const handleAllUnselect = () => {
    updateStructures(selectedStructures.filter((id) => !totalStructures.includes(id)))
  }

  const handleCheckAll = (e: CheckboxChangeEvent) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    e.target.checked ? handleAllSelect() : handleAllUnselect()
  }

  const renderTags = (): ReactElement<any, string | JSXElementConstructor<any>> => {
    let structuresTag = structureConfig
    if (selectedRadio === 'checked') {
      structuresTag = structuresTag.filter(({ id }) => selectedStructures.includes(id))
    } else if (filterList.length) {
      structuresTag = structuresTag
        .filter(({ efai_structure_name, customized_name }) => (
          (customized_name || efai_structure_name).toLowerCase().includes(searchText.toLowerCase())
        ))
        .filter(({ category }) => filterList.includes('All') || filterList.some((site) => category.includes(site)))
    }
    return structuresTag.length ? (
      <StructuresTagList
        dataSource={structureConfig.map((tagData) => ({
          ...tagData,
          checked: selectedStructures.includes(tagData.id),
        }))}
        tagProps={{
          seeColor,
          onChange(structures) {
            if (selectedRadio === 'checked') return
            handleUpdateStructures(structures.id)
          },
        }}
      />
    ) : (
      <section style={{
        height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '2rem 0',
      }}
      >
        <EmptyBox />
      </section>
    )
  }

  return (
    <section className="edit-structure">
      <menu
        className="edit-structure-navbar"
        ref={cascaderRef}
        style={(selectedRadio === 'checked') ? { display: 'none' } : {}}
      >
        <Flex
          component="ul"
          gap={20}
          style={{
            margin: 0,
            padding: 0,
            height: 28,
            listStyleType: 'none',
          }}
        >
          <li>
            <Checkbox
              onChange={seeColorChange}
              checked={seeColor}
              className="no-checkbox-border basic-shadow"
              style={{
                height: '100%',
              }}
            >
              {seeColor ? <EyeIcon /> : <EyeOffIcon />}
              {i18n.t('checkboxes.color_code')}
            </Checkbox>
          </li>
          <li>
            <MultipleTagSelect
              options={options}
              onChange={(v) => setFilterList(v)}
            />
          </li>
          <li>
            <Input
              size="small"
              placeholder={i18n.t('form_placeholders.search_structure')}
              onChange={(e) => { setSearchText(e.target.value) }}
              suffix={<SearchIcon />}
              className="placeholder-gray-1"
              style={{
                display: 'flex',
                height: 28,
                paddingInline: 12,
              }}
            />
          </li>
        </Flex>

        <Checkbox
          onChange={handleCheckAll}
          checked={isListChecked}
          indeterminate={isIndeterminate}
        >
          {i18n.t('checkboxes.select_all')}
        </Checkbox>
      </menu>

      <main className={`edit-structure-main ${selectedRadio === 'checked' && 'height-max'}`}>
        {renderTags()}
      </main>
    </section>
  )
}

export default EditStructureContent
