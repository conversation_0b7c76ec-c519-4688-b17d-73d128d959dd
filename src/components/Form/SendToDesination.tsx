import { useState, useRef, useEffect } from 'react'

import { Flex, Switch } from 'antd'
import { CSSTransition } from 'react-transition-group'

import i18n from 'src/i18n'
import { color } from 'src/utils/variables'
import 'src/styles/utils/transition.css'

type Props = {
  defaultOpen?: boolean
  onChange?: (checked: boolean) => void
  children?: React.ReactNode
}

function SendToDesination({
  defaultOpen = false,
  onChange,
  children,
}: Props) {
  const [isOpen, setIsOpen] = useState(false)
  const nodeRef = useRef<HTMLDivElement>(null)

  const handleSwitchChange = (checked: boolean) => {
    onChange?.(checked)
    setIsOpen(checked)
  }

  const openDirectionItems = ['directions.send_to_destination_open.1', 'directions.send_to_destination_open.2']
  const closeDirectionItems = ['directions.send_to_destination_close.1', 'directions.send_to_destination_close.2']
  const directionItems = isOpen ? openDirectionItems : closeDirectionItems

  useEffect(() => {
    if (!isOpen && defaultOpen) setIsOpen(true)
  }, [defaultOpen])

  return (
    <Flex vertical gap={16}>
      <div style={{ padding: 20, background: color.gray[2], borderRadius: 4 }}>
        <Flex align="center" gap={8} style={{ fontWeight: 700 }}>
          {i18n.t('switch_options.send_to_destination')}
          <Switch checked={isOpen} onChange={handleSwitchChange} />
        </Flex>
        <ul
          style={{
            margin: '8px 0 0',
            fontSize: 14,
            lineHeight: 1.75,
            color: color.gray[1].default,
          }}
        >
          {directionItems.map((i) => <li key={i}>{i18n.t(i)}</li>)}
        </ul>
      </div>
      <CSSTransition
        in={isOpen}
        timeout={300}
        classNames="collapse-transition"
        unmountOnExit
        nodeRef={nodeRef}
      >
        <div ref={nodeRef}>
          {children}
        </div>
      </CSSTransition>
    </Flex>
  )
}

export default SendToDesination
