import { useState } from 'react'
import type { JSXElementConstructor, ReactElement } from 'react'

import {
  Checkbox,
  ConfigProvider, Divider, Select, Tag, type SelectProps,
} from 'antd'

import { DownIcon, FilterIcon } from 'src/assets/icons'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

type Props = SelectProps & {}

const checkboxProps = {
  className: 'checklist-checkbox-item',
  style: {
    padding: 0,
    width: '100%',
  },
}
const fixIconStyle = {
  style: { fontSize: 24, color: color.gray[0].default },
}

const tagRender: SelectProps['tagRender'] = ({
  label, value, onClose, ...props
}) => {
  const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.preventDefault()
    event.stopPropagation()
  }

  if (label === 'All') return <span>{i18n.t('form_placeholders.filter')}</span>
  return (
    <Tag
      color={value}
      onMouseDown={onPreventMouseDown}
      style={{
        display: 'inline-flex',
        marginInlineEnd: 4,
        paddingInline: '.75rem',
      }}
      onClose={onClose}
      {...props}
    >
      {label}
    </Tag>
  )
}

const optionRender: SelectProps['optionRender'] = (option) => {
  return (
    <Checkbox checked={option.data.checked} {...checkboxProps}>
      {option.value}
    </Checkbox>
  )
}

const dropdownRender = (
  menu: ReactElement<any, string | JSXElementConstructor<any>>,
  isAll: boolean,
  onChange: (value: string[]) => void,
) => (
  <>
    <div style={{ padding: '.25rem .75rem' }}>
      <Checkbox
        {...checkboxProps}
        onChange={() => { onChange([]) }}
        checked={isAll}
      >
        {i18n.t('titles.all')}
      </Checkbox>
    </div>
    <Divider style={{ margin: '0', borderColor: color.gray[4] }} />
    {menu}
  </>
)

function MultipleTagSelect({
  style, options = [], onChange, ...selectProps
}: Props) {
  const [selection, setSelection] = useState<string[]>([])

  const handleChange = (value: string[]) => {
    onChange?.(value)
    setSelection(value)
  }

  return (
    <ConfigProvider theme={{
      components: {
        Select: {
          optionLineHeight: 2,
          selectorBg: color.gray[2],
          colorBorder: 'transparent',
          colorTextPlaceholder: color.gray[0].default,
          boxShadowSecondary: '0px 2px 0px 0px rgba(26, 38, 47, 0.4)',
          optionSelectedBg: color.gray[2],
          optionSelectedColor: color.primary.default,
          optionPadding: '4px 12px',
          controlItemBgHover: 'transparent',
          lineHeight: 1,
        },
        Tag: {
          defaultBg: ' var(--ffffff-15)',
          borderRadiusSM: 32,
        },
      },
    }}
    >
      <Select
        options={options.map((item) => ({
          ...item,
          checked: selection.includes(`${item.value}`),
        }))}
        value={selection}
        onChange={handleChange}
        mode="multiple"
        size="small"
        placeholder={i18n.t('form_placeholders.filter')}
        prefix={<FilterIcon {...fixIconStyle} />}
        suffixIcon={<DownIcon {...fixIconStyle} />}
        maxTagCount={4}
        tagRender={tagRender}
        dropdownRender={(menu) => dropdownRender(menu, selection.length === 0, handleChange)}
        optionRender={optionRender}
        showSearch={false}
        rootClassName="basic-shadow prefix-center select-multiple-tags"
        style={{
          minWidth: 120,
          ...style,
        }}
        dropdownStyle={{
          minWidth: 150,
          maxWidth: 180,
          padding: 0,
        }}
        {...selectProps}
      />
    </ConfigProvider>
  )
}

export default MultipleTagSelect
