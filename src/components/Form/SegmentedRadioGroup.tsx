import {
  Flex, Radio, type RadioGroupProps, type FlexProps,
} from 'antd'

import { OkCircleBlueIcon, OkCircleIcon } from 'src/assets/icons'

type Props = RadioGroupProps & {}

function Text({
  style, checked, children, ...props
}: FlexProps & { checked?: boolean }) {
  return (
    <Flex
      gap={2}
      style={{ textAlign: 'center', ...style }}
      align="center"
      justify="space-between"
      {...props}
    >
      {checked ? <OkCircleBlueIcon /> : <OkCircleIcon />}
      {children}
    </Flex>
  )
}

function SegmentedRadioGroup({
  options = [], className, children, ...radioGroupProps
}: Props) {
  return (
    <Radio.Group
      optionType="button"
      buttonStyle="solid"
      size="small"
      className={`radio-not-center-driver ${className}`}
      {...radioGroupProps}
      style={{
        ...radioGroupProps?.style,
      }}
    >
      {children || options.map((option) => {
        if (typeof option === 'string' || typeof option === 'number') {
          return (
            <Radio key={option} value={option} style={{ width: 75 }}>
              <Text checked={option === radioGroupProps.value}>{option}</Text>
            </Radio>
          )
        }
        return (
          <Radio key={option.value} value={option.value} style={{ width: 75 }}>
            <Text checked={option.value === radioGroupProps.value}>{option.label}</Text>
          </Radio>
        )
      })}
    </Radio.Group>
  )
}

export default SegmentedRadioGroup
