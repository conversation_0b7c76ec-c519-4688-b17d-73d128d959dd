import { Input } from 'antd'
import { capitalize } from 'lodash-es'

import i18n from 'src/i18n'

type Props = {
  id?: number
  name: string
  value?: string
  handleChange: (id: number, fieldName: string, data: any) => void
  required?: boolean
  duplication?: boolean
  errorText?: string
  customizePlaceholder?: string
}
const { TextArea } = Input
function TableInput({
  id, name, value, handleChange, required, duplication, errorText, customizePlaceholder,
}: Props) {
  return (
    <>
      <TextArea
        autoSize
        placeholder={customizePlaceholder || i18n.t('form_placeholders.enter_variable', {
          variable: i18n.t(`titles.${name}`),
          joinArrays: ' ',
        })}
        value={value}
        onChange={(e: any) => (id !== undefined && handleChange(id, name, e.target.value))}
        status={(required && !value) || (duplication && value) || errorText ? 'error' : undefined}
        key={`${name}-${id}`}
        className="list-item-input"
      />
      {(required && !value) && (
        <div style={{ color: 'var(--color-error)' }}>
          {i18n.t('form_rules.enter_variable', {
            variable: i18n.t(`plain_texts.${name}`),
            joinArrays: ' ',
          })}
        </div>
      )}
      {(duplication && value) && (
        <div style={{ color: 'var(--color-error)' }}>
          {i18n.t('form_rules.variable_unique', {
            variable: capitalize(i18n.t(`plain_texts.${name}`)),
            joinArrays: ' ',
          })}
        </div>
      )}
      {errorText && (
        <div style={{ color: 'var(--color-error)' }}>
          {errorText}
        </div>
      )}
    </>
  )
}

export default TableInput
