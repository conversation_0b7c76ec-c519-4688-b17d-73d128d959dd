import { Form, Input, Space } from 'antd'
import { FormInstance } from 'antd/es/form'

import Button from 'src/components/Button'
import i18n from 'src/i18n'

interface Props {
  handleSearch: () => void
  form: FormInstance<any>
}

function Search({ form, handleSearch }: Props) {
  const deleteInputValue = () => {
    form.resetFields()
    handleSearch()
  }

  return (
    <Form
      form={form}
      name="search"
      onFinish={handleSearch}
    >
      <Form.Item
        name="searchName"
        style={{ marginBottom: 0 }}
      >
        <Space direction="horizontal">
          <Input
            placeholder={i18n.t('form_placeholders.search_name')}
            onChange={(e) => e.type === 'click' && deleteInputValue()}
            allowClear
          />
          <Button type="submit" className="btn text-gray-4">
            {i18n.t('buttons.search')}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  )
}

export default Search
