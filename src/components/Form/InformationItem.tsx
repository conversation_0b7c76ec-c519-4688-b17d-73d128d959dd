import { useState } from 'react'

import {
  Checkbox, Col, Flex, Form, Input, Modal, Select, Switch,
} from 'antd'

import useAlert from 'src/hooks/useAlert'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useGetWorklistProtocolDetailMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import type { ConfigRequiredKey } from 'src/store/reducers/configSlice'
import { updateDetail } from 'src/store/reducers/detailSlice'
import { updateWorklistDetail, updateWorklistProtocol } from 'src/store/reducers/worklistSlice'

interface Props {
  fieldName: ConfigRequiredKey
  fieldValue: string | number
  headerLength: number
  informationType: 'worklist' | 'protocol'
}

function InformationItem({
  fieldName, fieldValue, headerLength, informationType,
}: Props) {
  const requiredFields = ['protocol_name', 'structure_set_label'].includes(fieldName)
  const [useModal, setUseModal] = useState<boolean>(true)
  const [selectValue, setSelectValue] = useState<string>('')
  const { protocolConfig, configRequired } = useAppSelector((state) => state.configReducer)
  const [getWorklistProtocolDetailMutation] = useGetWorklistProtocolDetailMutation()
  const dispatch = useAppDispatch()
  // hook
  const handleAlert = useAlert()

  const isEditIcon = (value: string): boolean => {
    const noEditList = [
      'patient_id',
      'use_protocol',
      'study_date',
      'series_time',
      'study_description',
      'series_description',
      'status',
    ]
    return noEditList.includes(value)
  }

  const handleSwitchChange = (bool: boolean) => {
    dispatch(updateDetail({ status: bool ? 'ACTIVE' : 'INACTIVE' }))
  }

  const handleInputChange = (value: string) => {
    if (informationType === 'worklist') {
      dispatch(updateWorklistDetail({ [fieldName]: value }))
    } else {
      dispatch(updateDetail({ [fieldName]: value }))
    }
  }

  const handleProtocolChange = async (value?: string) => {
    const protocolId = Number(value) || Number(selectValue)
    if (!protocolId) return
    try {
      await getWorklistProtocolDetailMutation({ id: protocolId }).unwrap()
      dispatch(updateWorklistProtocol({ value: protocolId }))
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const colWidth = (value: string): number => {
    const actions: { [key: string]: () => number } = {
      xl(): number {
        if (headerLength > 6) return 8
        return 9
      },
      xxl(): number {
        if (headerLength > 6) return 8
        return 9
      },
    }
    return actions[value]()
  }

  // modal
  const modal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.change_protocol_confirmation'),
      okText: i18n.t('buttons.ok'),
      onOk() {
        handleProtocolChange()
      },
      centered: true,
      width: 350,
      styles: {
        body: { padding: '2.5rem 2rem' },
      },
    },
  })

  const handleSelectChange = async (value: string) => {
    if (useModal) {
      modal.trigger()
    }
    setSelectValue(value)
  }

  const isChecked = (fieldName as string) === 'status' && fieldValue === 'ACTIVE'
  const elementList: { [key: string]: JSX.Element } = {
    status: (
      <Flex gap={8} align="center" justify="space-between" style={{ width: '100px' }}>
        <span>
          {isChecked ? i18n.t('switch_options.active') : i18n.t('switch_options.inactive')}
        </span>
        <Switch
          id={fieldName}
          checked={isChecked}
          onChange={handleSwitchChange}
        />
      </Flex>
    ),
    use_protocol: (
      <Select
        style={{ width: '80%', marginLeft: '20px' }}
        onChange={useModal ? handleSelectChange : handleProtocolChange}
        options={protocolConfig.map((item) => ({ value: item.id, label: item.protocol_name }))}
        value={
          fieldValue
            ? protocolConfig.find((item) => item.id === fieldValue)?.protocol_name
            : i18n.t('titles.customized')
        }
      />
    ),
  }

  const renderElement = (value: string) => {
    const noEditList = [
      'patient_id',
      'study_date',
      'series_time',
      'study_description',
      'series_description',
    ]
    if (noEditList.includes(value)) {
      return (
        <span style={{ color: 'var(--color-gray_0)' }}>
          {fieldValue}
        </span>
      )
    }

    return (elementList[value] ? elementList[value] : (
      <Input
        maxLength={1000}
        placeholder={i18n.t('form_placeholders.enter_variable', {
          variable: i18n.t(`titles.${fieldName}`),
          joinArrays: ' ',
        })}
        value={fieldValue}
        onChange={(e) => handleInputChange(e.target.value)}
        className="input-none-style focus-default remove-spinners"
        style={{ resize: 'none', padding: '0 .5rem' }}
      />
    ))
  }

  return (
    <Col
      xxl={colWidth('xxl')}
      xl={colWidth('xl')}
      span={12}
      key={fieldName}
    >
      <Form.Item
        label={i18n.t(`titles.${fieldName}`)}
        required={requiredFields}
        validateStatus={(requiredFields && configRequired[fieldName]) ? 'error' : ''}
        style={{ color: 'var(--color-gray_5)' }}
        className={isEditIcon(fieldName) ? '' : 'edit-icon'}
      >
        {renderElement(fieldName)}
      </Form.Item>
      {(fieldName as string) === 'use_protocol'
        && (
          <Modal {...modal.modalProps}>
            <p style={{
              marginBottom: '1.5rem',
              textAlign: 'center',
            }}
            >{i18n.t('modal_contents.change_protocol_confirmation')}
            </p>
            <Checkbox
              onChange={(e) => setUseModal(!e.target.checked)}
            >
              {i18n.t('checkboxes.not_ask_again')}
            </Checkbox>
          </Modal>
        )}
    </Col>
  )
}

export default InformationItem
