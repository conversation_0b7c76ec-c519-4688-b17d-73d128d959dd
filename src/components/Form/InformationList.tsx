import { Form, Row } from 'antd'

import InformationItem from 'src/components/Form/InformationItem'
import type { ConfigDescriptionItem } from 'src/context/moduleList'
import type { ConfigRequiredKey } from 'src/store/reducers/configSlice'

interface Props<T extends ProtocolDetailType | WorklistDetailType> {
  configHeader: ConfigDescriptionItem[]
  configData: T | null
  informationType: 'worklist' | 'protocol'
}

function InformationList<T extends ProtocolDetailType | WorklistDetailType>({
  configHeader,
  configData,
  informationType,
}: Props<T>) {
  return (
    <Form
      wrapperCol={{ span: 16 }}
      labelWrap
      layout="horizontal"
      className="edit-form"
      style={{ minHeight: '120px' }}
    >
      <Row gutter={0} style={{ margin: '0' }}>
        {configHeader.map((cell) => (
          <InformationItem
            key={cell.label as string}
            fieldName={cell.label as ConfigRequired<PERSON><PERSON>}
            fieldValue={configData ? configData[cell.label as keyof CommonInfo] : ''}
            headerLength={configHeader.length}
            informationType={informationType}
          />
        ))}
      </Row>
    </Form>
  )
}

export default InformationList
