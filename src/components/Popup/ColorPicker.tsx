import React, { useState } from 'react'

import { ColorPicker as AntColorPicker, type ColorPickerProps } from 'antd'

import { DownIcon } from 'src/assets/icons'

function ColorPicker({ ...props }: ColorPickerProps) {
  const [open, setOpen] = useState(false)

  return (
    <AntColorPicker
      open={open}
      onOpenChange={setOpen}
      disabledAlpha
      defaultValue="#039BA0"
      // eslint-disable-next-line react/no-unstable-nested-components
      showText={() => (
        <DownIcon style={{ transform: `rotate(${open ? 180 : 0}deg)` }} />
      )}
      {...props}
    />
  )
}
export default ColorPicker
