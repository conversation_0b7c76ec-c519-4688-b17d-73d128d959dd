import { useEffect } from 'react'

import { Button, Form } from 'antd'
import { Content } from 'antd/es/layout/layout'

import { useAntModal } from '@/hooks/useAntModal'
import { Search } from 'src/components/Form'
import { Head } from 'src/components/Head'
import useAlert from 'src/hooks/useAlert'
import i18n from 'src/i18n'
import { useLazyGetRsTemplatesQuery } from 'src/services/api'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { setSearchModeCheck } from 'src/store/reducers/rsTemplatesSlice'

import CreateNewTemplatesModal from './rs-template-list/CreateNewTemplatesModal'
import RsTemplatesTable from './rs-template-list/RsTemplatesTable'

import 'src/styles/pages/protocols.css'

function RsTemplateListPage() {
  const [form] = Form.useForm()

  const { updateProtocolSortCheck } = useAppSelector((state) => state.protocolReducer)
  const [trigger, { isFetching }] = useLazyGetRsTemplatesQuery()
  const dispatch = useAppDispatch()
  // hook
  const handleAlert = useAlert()

  const getRsTemplates = () => {
    const name = form.getFieldsValue().searchName
    // In search mode order can't be changed
    dispatch(setSearchModeCheck(!!name))

    try {
      trigger()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  // modal
  const createModal = useAntModal({
    triggerProps: {
      disabled: updateProtocolSortCheck,
    },
    modalProps: {
      title: i18n.t('modal_titles.create_new_modal', { name: i18n.t('titles.template'), joinArrays: ' ' }),
      okText: i18n.t('buttons.next'),
      width: '765px',
      className: 'create-new-modal',
      styles: {
        header: {
          padding: '0 30px',
        },
        body: {
          height: '70vh',
          minHeight: 600,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
      },
    },
  })

  useEffect(() => {
    getRsTemplates()
  }, [])

  return (
    <>
      <Head>{i18n.t('titles.rs_templates')}</Head>
      <Content style={{ gap: '1.5rem' }}>
        <section style={{ overflowX: 'auto' }}>
          <nav style={{
            width: '100%',
            minWidth: '673px',
            display: 'flex',
            justifyContent: 'space-between',
          }}
          >
            <Search form={form} handleSearch={getRsTemplates} />
            <Button
              htmlType="button"
              className="gray"
              {...createModal.triggerProps}
            >
              {i18n.t('buttons.new_template')}
            </Button>
          </nav>
        </section>
        <RsTemplatesTable spinning={isFetching} reload={getRsTemplates} />
        <CreateNewTemplatesModal {...createModal} />
      </Content>
    </>
  )
}

export default RsTemplateListPage
