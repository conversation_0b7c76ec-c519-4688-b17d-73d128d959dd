import React from 'react'

import type { DragEndEvent } from '@dnd-kit/core'
import {
  Button, Flex, Modal, Switch,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { Link } from 'react-router'

import {
  DeleteIcon, EditIcon, LoadingIcon2,
} from 'src/assets/icons'
import SortTable, { DragHandle } from 'src/components/Table/SortTable'
import useAlert from 'src/hooks/useAlert'
import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { usePatchProtocolStatusMutation, useDeleteProtocolMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { deleteProtocol, updateProtocolSort } from 'src/store/reducers/protocolSlice'
import { withoutTime } from 'src/utils/helper'

import 'src/styles/components/tableLayout.css'

interface Props {
  spinning: boolean
  reload: () => void
}

interface ColumnDataType {
  key: number
  id: number
  protocol_name: string
  description: string | undefined
  last_mod_time: string
  status: string
}

function ProtocolTable({ spinning, reload }: Props) {
  const { protocols, protocolSearchModeCheck } = useAppSelector((state) => state.protocolReducer)
  const [patchProtocolStatusMutation] = usePatchProtocolStatusMutation()
  const [deleteProtocolMutation] = useDeleteProtocolMutation()
  const dispatch = useAppDispatch()
  // hook
  const handleAlert = useAlert()
  const deleteModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.delete_confirmation'),
      onOk: async (id: number) => {
        try {
          await deleteProtocolMutation({ id }).unwrap()
          dispatch(deleteProtocol({ id }))
          reload()
        } catch (e) {
          handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
        }
      },
      width: 350,
      styles: { body: { padding: '4.75rem 0' } },
    },
  })

  const dataSource = protocols.map((item) => ({
    key: item.priority,
    id: item.id,
    protocol_name: item.protocol_name,
    description: item.description,
    last_mod_time: withoutTime(item.last_mod_time),
    status: item.status,
  }))

  const handleSwitchChange = async (id: number, status: string) => {
    try {
      await patchProtocolStatusMutation({ id, status })
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      dispatch(updateProtocolSort({ activeId: active.id, overId: over?.id }))
    }
  }

  const columns: ColumnsType<ColumnDataType> = [
    {
      key: 'sort',
      width: '25px',
      render: () => (
        <DragHandle
          style={protocolSearchModeCheck ? { cursor: 'not-allowed' } : { cursor: 'move' }}
          listening={!protocolSearchModeCheck}
        />
      ),
    },
    {
      title: i18n.t('titles.priority'),
      key: 'priority',
      width: 80,
      render: (_id, _record, index) => <p style={{ width: 40 }}>{index + 1}</p>,
    },
    {
      title: i18n.t('titles.protocol_name'),
      dataIndex: 'protocol_name',
      key: 'protocol_name',
      render(_, { protocol_name }) {
        return (<div style={{ textWrap: 'nowrap' }}> {protocol_name}</div>)
      },
    },
    {
      title: i18n.t('titles.last_modified'),
      dataIndex: 'last_mod_time',
      key: 'last_mod_time',
      render(_, { last_mod_time }) {
        return (<div style={{ textWrap: 'nowrap' }}> {last_mod_time}</div>)
      },
      width: '200px',
    },
    {
      title: i18n.t('titles.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: i18n.t('titles.status'),
      key: 'status',
      width: '130px',
      render: (_, { id, status }: ColumnDataType) => (
        <Flex gap={8} align="center" justify="space-between" style={{ width: '98px' }}>
          <span>
            {status === 'ACTIVE' ? i18n.t('switch_options.active') : i18n.t('switch_options.inactive')}
          </span>
          <Switch
            id={`${id}`}
            checked={status === 'ACTIVE'}
            onChange={(checked: boolean) => {
              handleSwitchChange(id, checked ? 'ACTIVE' : 'INACTIVE')
            }}
          />
        </Flex>
      ),
    },
    {
      title: i18n.t('titles.operation'),
      key: 'operation',
      width: '120px',
      render: (_, { id, protocol_name }: ColumnDataType) => (
        <div className="icons-btn-group">
          <Button
            className="basic-shadow"
            size="small"
            onClick={() => deleteModal.trigger(id, {
              children: i18n.t(
                'modal_contents.delete_confirmation',
                { item: `"${protocol_name}"`, joinArrays: ' ' },
              ),
            })}
            icon={<DeleteIcon />}
          />
          <Link className="icon-only" to={`${id}`} style={{ fontSize: 28, borderRadius: 4 }}>
            <EditIcon />
          </Link>
        </div>
      ),
    },
  ]

  return (
    <section style={{ flex: '1 0 0' }}>
      <SortTable
        sortableItems={dataSource.map((i) => i.id)}
        dndContextProps={{ onDragEnd }}
        scroll={{ x: true, y: 'calc(100% - 45px)' }}
        className="dnd-table setting-table-container"
        rowKey="id"
        columns={columns}
        dataSource={dataSource}
        loading={{
          spinning,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
      />
      <Modal {...deleteModal.modalProps} />
    </section>
  )
}

export default ProtocolTable
