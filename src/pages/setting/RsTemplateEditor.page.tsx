import React, { useState } from 'react'

import { type TabsProps, Button } from 'antd'
import { useParams } from 'react-router'

import SendToDesination from '@/components/Form/SendToDesination'
import ConfigTabItemLabel from '@/components/Tabs/ConfigTabItemLabel'
import TransferTabs from '@/components/Transfer/TransferTabs'
import { ConfigDescriptionItem } from '@/context/moduleList'
import i18n from '@/i18n'
import { BasicLayout, StructuresTabs } from '@/layouts/NewConfigLayout'
import { useAppSelector } from '@/store/hook'

// You'll need to create this hook similar to useProcessStructuresDetailState
// import useRsTemplateEditorState from './hooks/useRsTemplateEditorState'

interface RsTemplateState {
  template_name: string
  template_description: string
  rs_set_label: string
  destination: {
    remote_server: RemoteType[]
    folder: RemoteType[]
  }
}

function RsTemplateEditorPage() {
  const { id } = useParams()

  // TODO: Use id to load template data when implementing the hook

  // Temporary state - replace with proper hook implementation
  const [detailState, setDetailState] = useState<RsTemplateState>({
    template_name: 'BreastL4256',
    template_description: '',
    rs_set_label: '',
    destination: {
      remote_server: [],
      folder: [],
    },
  })

  const [loading] = useState(false)
  const [leaveBlocker] = useState(false)

  const { configRequired } = useAppSelector((state) => state.configReducer)

  // Handlers - implement these based on your requirements
  const handleChangeInput = (field: keyof RsTemplateState, value: string) => {
    setDetailState((prev) => ({ ...prev, [field]: value }))
  }

  const handleUpdateRemote = () => {
    // Implement remote update logic
  }

  const handleRemoveDestination = () => {
    setDetailState((prev) => ({
      ...prev,
      destination: {
        remote_server: [],
        folder: [],
      },
    }))
  }

  const handleSave = () => {
    // Implement save logic
    // TODO: Add actual save implementation
  }

  // Config header based on the image structure
  const configHeader: ConfigDescriptionItem[] = [
    {
      label: 'template_name',
      type: 'input',
      required: true,
      props: {
        value: detailState.template_name,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('template_name', e.target.value)
        },
        status: configRequired.name && 'error',
      },
    },
    {
      label: 'rs_set_label',
      tooltip: i18n.t('tooltips.structure_set_label'),
      type: 'input',
      required: true,
      props: {
        value: detailState.rs_set_label,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('rs_set_label', e.target.value)
        },
        status: configRequired.structure_set_label && 'error',
      },
    },
    {
      label: 'template_description',
      type: 'input',
      props: {
        value: detailState.template_description,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('template_description', e.target.value)
        },
      },
    },
  ]

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <ConfigTabItemLabel warn={configRequired.destination}>
          {i18n.t('titles.destination')}
        </ConfigTabItemLabel>
      ),
      children: (
        <SendToDesination
          defaultOpen={Object.values(detailState.destination).some((list) => list.length > 0)}
          onChange={(checked) => !checked && handleRemoveDestination()}
        >
          <TransferTabs
            type="destination"
            checkList={detailState}
            onUpdateRemote={handleUpdateRemote}
          />
        </SendToDesination>
      ),
    },
    {
      key: '2',
      label: (
        <ConfigTabItemLabel warn={(configRequired.structures || !!configRequired.customized_structures?.length)}>
          {i18n.t('titles.structures')}
        </ConfigTabItemLabel>
      ),
      children: (
        <StructuresTabs />
      ),
    },
  ]

  return (
    <BasicLayout
      header={{
        navigatePage: '/setting',
        children: i18n.t('titles.rs_template_editor'),
      }}
      descriptionData={configHeader}
      loading={loading}
      tabs={tabItems}
      footer={(
        <Button color="primary" variant="outlined" onClick={handleSave}>
          {i18n.t('buttons.save')}
        </Button>
      )}
      leaveBlocker={leaveBlocker}
    />
  )
}

export default RsTemplateEditorPage
