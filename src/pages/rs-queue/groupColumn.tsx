import type { ColumnsType } from 'antd/es/table/interface'

import i18n from 'src/i18n'
import { withoutTime } from 'src/utils/helper'
import { color } from 'src/utils/variables'

export const groupColumn: ColumnsType<RsQueueGroupType> = [
  {
    title: i18n.t('titles.patient_id'),
    dataIndex: 'patient_id',
    render: (id) => <div style={{ paddingLeft: '16px' }}>{id}</div>,
    key: 'patient_id',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    ellipsis: true,
  },
  {
    title: i18n.t('titles.patient_name'),
    dataIndex: 'patient_name',
    key: 'patient_name',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
  },
  {
    title: i18n.t('titles.study_date'),
    dataIndex: 'study_date',
    key: 'study_date',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    render: (_, { study_date }) => (
      <p style={{
        minWidth: 90,
        color: color.gray[1].default,
      }}
      >
        {withoutTime(study_date)}
      </p>
    ),
  },
]
