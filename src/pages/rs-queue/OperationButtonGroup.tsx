import { Button, Flex } from 'antd'

import { BooleanIcon, DeleteIcon } from '@/assets/icons'
import { TriggerProps } from '@/hooks/useAntModal'

import { baseTriggerProps } from '../../utils/saveButton'

interface Props {
  navigateProps?: TriggerProps<unknown>
  deleteProps?: TriggerProps<unknown>
}

function OperationButtonGroup({
  navigateProps, deleteProps,
}: Props) {
  return (
    <Flex gap={8} key="operation">
      <Button {...baseTriggerProps} {...navigateProps}><BooleanIcon /></Button>
      <Button {...baseTriggerProps} {...deleteProps}><DeleteIcon /></Button>
    </Flex>
  )
}

export default OperationButtonGroup
