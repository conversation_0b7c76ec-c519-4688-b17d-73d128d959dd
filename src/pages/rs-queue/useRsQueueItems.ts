import {
  useGetRsQueueDetailQuery,
  useDeleteRsQueueItemMutation,
} from 'src/services/api'
import { useAppSelector } from 'src/store/hook'

export function useRsQueueItems() {
  const { rsQueueGroupFocus, rsQueueGroups } = useAppSelector((state) => state.rsQueueReducer)

  // Find the selected group by study_uid
  const selectedGroup = rsQueueGroups.find((group) => group.study_uid === rsQueueGroupFocus)

  // Get RS queue detail for the selected group
  const {
    data: rsQueueDetailData,
    isLoading: isLoadingItems,
    refetch: refetchItems,
  } = useGetRsQueueDetailQuery(
    { study_uid: selectedGroup?.study_uid || '' },
    { skip: !selectedGroup?.study_uid },
  )

  // Mutations
  const [deleteItem] = useDeleteRsQueueItemMutation()

  const rsQueueItems = rsQueueDetailData?.data || []

  const handleCheckStatus = async () => 'SUCCEEDED'

  const handleDeleteItem = async (itemId: number) => {
    await deleteItem({ rs_id: itemId }).unwrap()
    // Refetch items after deletion
    refetchItems()
  }

  return {
    rsQueueItems,
    isLoadingItems,
    refetchItems,
    handleCheckStatus,
    handleDeleteItem,
    selectedGroup,
  }
}
