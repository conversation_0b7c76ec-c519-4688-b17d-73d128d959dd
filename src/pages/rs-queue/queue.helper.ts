import i18n from '@/i18n'
import { WorkStatusEnum } from '@/utils/enum'

type WorkStatusEnumKey = typeof WorkStatusEnum[keyof typeof WorkStatusEnum]

function getErrorMessage(params: WorkStatusEnumKey): string[] {
  switch (params) {
    case WorkStatusEnum.NO_MATCH:
      return [i18n.t('modal_contents.error_no_matching.text'), i18n.t('modal_contents.error_no_matching.subText')]
    case WorkStatusEnum.FAILED:
      return [i18n.t('modal_contents.error_failed.text'), i18n.t('modal_contents.error_failed.subText')]
    default:
      return []
  }
}

export { getErrorMessage }
