import { useState } from 'react'

import {
  Button, Flex, Modal, Table,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router'

import { XCircleIcon } from '@/assets/icons'
import Loading from '@/components/Loading'
import { useAntModal } from '@/hooks/useAntModal'
import i18n from '@/i18n'
import SubTableLayout from '@/layouts/SubTableLayout'
import { WorkStatusEnum } from '@/utils/enum'
import { color } from '@/utils/variables'

import OperationButtonGroup from './OperationButtonGroup'
import { getErrorMessage } from './queue.helper'
import { useRsQueueItems } from './useRsQueueItems'

function SubTable() {
  const [isLoading, setIsLoading] = useState(false)
  const navigate = useNavigate()

  // Use RS Queue Items hook
  const {
    rsQueueItems,
    isLoadingItems,
    handleCheckStatus,
    handleDeleteItem,
  } = useRsQueueItems()

  const errorModal = useAntModal<WorkStatusEnum>({
    modalProps: {
      title: i18n.t('error_titles.'),
      destroyOnClose: true,
      width: 400,
      styles: {
        body: { padding: '3.5rem .5rem' },
      },
      centered: true,
      render(value) {
        return (
          <Flex vertical gap={8} align="center">
            <div style={{ color: color.error, fontSize: 54 }}><XCircleIcon /></div>
            <p style={{ textAlign: 'center', lineHeight: 2.25 }}>
              <span>{getErrorMessage(value)?.[0]}</span>
              <br />
              <span style={{ color: color.gray[1].default }}>{getErrorMessage(value)?.[1]}</span>
            </p>
          </Flex>
        )
      },
    },
  })

  const removeModal = useAntModal<ImageRSList>({
    modalProps: {
      async onOk(record) {
        if (record) {
          try {
            await handleDeleteItem(record.id)
          } catch (error) {
            // Handle delete error if needed
          }
        }
      },
      title: i18n.t('modal_titles.remove_confirmation'),
      okText: i18n.t('buttons.remove'),
      destroyOnClose: true,
      width: 400,
      styles: {
        body: { padding: '3.5rem .5rem' },
      },
      centered: true,
    },
  })

  const handleNavigate = async (record: ImageRSList) => {
    setIsLoading(true)
    try {
      await handleCheckStatus()
      navigate(`/rs/queue/${record.id}`)
    } catch (error: unknown) {
      const errorType = (error as Error).message || WorkStatusEnum.FAILED
      errorModal.trigger(errorType as WorkStatusEnum)
    } finally {
      setIsLoading(false)
    }
  }

  const columns: ColumnsType<ImageRSList> = [
    {
      title: 'Operation',
      key: 'operation',
      width: '100px',
      render: (_, record) => (
        <OperationButtonGroup
          navigateProps={{ onClick: () => handleNavigate(record) }}
          deleteProps={{ onClick: () => removeModal.trigger(record) }}
        />
      ),
    },
    {
      title: 'RS Set Label',
      key: 'rs_set_label',
      dataIndex: 'rs_set_label',
    },
    {
      title: (
        <p style={{
          marginBottom: 8,
          whiteSpace: 'wrap',
          lineHeight: 1.5,
        }}
        >
          Series Number
        </p>
      ),
      key: 'series_number',
      dataIndex: 'series_number',
      width: 100,
    },
    {
      title: 'RS Set Date',
      key: 'rs_set_date',
      dataIndex: 'rs_set_date',
      width: 125,
    },
    {
      title: 'Import Time',
      key: 'import_time',
      dataIndex: 'import_time',
      width: 125,
    },
  ]

  return (
    <SubTableLayout
      theme={{
        components: {
          Table: {
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[2],
            headerBg: color.gray[3],
            headerColor: color.gray[0].default,
            headerSortHoverBg: color.gray[2],
            headerSortActiveBg: color.gray[3],
            bodySortBg: color.gray[3],
            colorPrimary: color.primary.default,
            cellPaddingBlock: 8,
            cellPaddingInline: 16,
          },
        },
      }}
      table={{ className: 'series-table-container' }}
    >
      <Table
        tableLayout="auto"
        columns={columns}
        dataSource={rsQueueItems}
        loading={isLoadingItems}
        pagination={false}
        rowKey={(record) => record.id}
        scroll={{ x: true, y: '100%' }}
      />

      {/* Error Modal */}
      <Modal
        {...errorModal.modalProps}
        footer={(
          <footer>
            <Button color="primary" variant="outlined" onClick={errorModal.dismiss}>{i18n.t('buttons.close')}</Button>
          </footer>
        )}
      />

      {/* Remove Modal */}
      <Modal {...removeModal.modalProps}>
        <Flex vertical gap={8} align="center">
          <p>{i18n.t('modal_contents.remove_rs_confirmation')}</p>
        </Flex>
      </Modal>

      {/* Loading Modal */}
      <Loading open={isLoading} />
    </SubTableLayout>
  )
}

export default SubTable
