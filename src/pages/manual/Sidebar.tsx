import { useRef } from 'react'

import {
  Anchor, Button, Divider, Layout, type AnchorProps,
} from 'antd'
import { useNavigate } from 'react-router'

import { QuestionCircleOutlined } from 'src/assets/icons'

const { Sider } = Layout
type Props = {
  isFAQ: boolean
  anchorProps?: AnchorProps
}

function Sidebar({ isFAQ, anchorProps }: Props) {
  const navigate = useNavigate()
  const navRef = useRef<HTMLElement>(null)

  return (
    <Sider width={300} className="manual-layout-sidebar">
      <Button
        block
        type="text"
        className={`faq-btn ${isFAQ && 'faq-btn-active'}`}
        onClick={() => {
          navigate('/manual')
          if (navRef.current) {
            navRef.current.scrollTo({ top: 0, behavior: 'smooth' })
          }
        }}
      >
        <QuestionCircleOutlined /> FAQ
      </Button>
      <Divider />
      <nav ref={navRef} className="manual-layout-sidebar-content">
        <Anchor
          className="manual-layout-sidebar-anchor"
          affix={false}
          targetOffset={24}
          onClick={(_, { href }) => {
            navigate(`directions${href}`)
          }}
          {...anchorProps}
        />
      </nav>
    </Sider>
  )
}

export default Sidebar
