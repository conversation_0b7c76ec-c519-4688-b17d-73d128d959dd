import { Checkbox, Flex } from 'antd'
import { useNavigate } from 'react-router'

import { WorkStatusEnum } from 'src/utils/enum'

import PreviewTrigger from '../trigger/PreviewTrigger'
import RedrawTrigger from '../trigger/RedrawTrigger'

interface Props {
  worklistId?: number
  seriesInfo?: PreviewSeriesInfoType
  status?: string
  isSwitch?: boolean
  redrawDisabled?: boolean
  onCheckChange?: (checked: boolean, seriesId?: string) => void
  seriesId?: string
  checkList?: {
    series_ids: string[]
    worklist_ids: number[]
  }
  clearCheckList?: () => void
}

function SeriesButtonGroup({
  worklistId,
  seriesInfo,
  status = WorkStatusEnum.SUCCEEDED,
  isSwitch,
  redrawDisabled = false,
  onCheckChange,
  seriesId,
  checkList = { series_ids: [], worklist_ids: [] },
  clearCheckList,
}: Props) {
  const navigate = useNavigate()

  // Check if this series is selected (using seriesId)
  const isChecked = seriesId ? checkList.series_ids.includes(seriesId) : false
  const handleRedraw = () => {
    clearCheckList?.()
    navigate(`${worklistId}`)
  }

  return (
    <Flex component="nav" gap={8} className="icons-btn-group">
      {!isSwitch && (
        <>
          <PreviewTrigger
            disabled={status !== WorkStatusEnum.SUCCEEDED}
            worklistId={worklistId}
            seriesInfo={seriesInfo}
          />
          <RedrawTrigger
            disabled={redrawDisabled}
            onClick={handleRedraw}
          />
        </>
      )}
      {isSwitch && (
        <div style={{ marginLeft: 1, width: 152 }}>
          <Checkbox
            checked={isChecked}
            onChange={(e) => onCheckChange?.(e.target.checked, seriesId)}
          />
        </div>
      )}
    </Flex>
  )
}

export default SeriesButtonGroup
