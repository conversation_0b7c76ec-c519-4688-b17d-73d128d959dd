import {
  Button, Checkbox, Flex, Tooltip,
} from 'antd'

import TooltipTitle from '@/components/TooltipTitle'
import i18n from '@/i18n'
import { CancelIcon, DownloadIcon, ResendIcon } from 'src/assets/icons'
import { WorkStatusEnum } from 'src/utils/enum'
import { baseTriggerProps } from 'src/utils/saveButton'

import { CheckListResult } from '../../hooks/useCheckListReducer'
import ResendTrigger from '../trigger/ResendTrigger'

interface Props extends CheckListResult {
  worklistIds?: number[]
  seriesIds?: string[]
  status?: string
  studyId?: string
}

enum CheckStateEnum {
  resend = 'resend',
  download = 'download',
  none = '',
}

function StudyButtonGroup({
  worklistIds,
  seriesIds,
  status = WorkStatusEnum.SUCCEEDED,
  studyId,
  isSwitch, switchType, checkList, activeStudyId,
  updateCheckList, toggleSwitch, startSwitch,
}: Props) {
  const disabled = ([WorkStatusEnum.REMOVED, WorkStatusEnum.NO_MATCH] as string[]).includes(status)

  // Check if this specific study is in switch mode
  const isThisStudyInSwitchMode = isSwitch && activeStudyId === studyId

  // Check if all items (both series and worklist) are selected
  const allWorklistSelected = worklistIds && worklistIds.length > 0
    && worklistIds.every((id) => checkList.worklist_ids.includes(id))
  const allSeriesSelected = seriesIds && seriesIds.length > 0
    && seriesIds.every((id) => checkList.series_ids.includes(id))
  const isAllSelected = allWorklistSelected && allSeriesSelected

  // Check if some (but not all) items are selected
  const someWorklistSelected = worklistIds && worklistIds.length > 0
    && worklistIds.some((id) => checkList.worklist_ids.includes(id))
  const someSeriesSelected = seriesIds && seriesIds.length > 0
    && seriesIds.some((id) => checkList.series_ids.includes(id))
  const isIndeterminate = (someWorklistSelected || someSeriesSelected) && !isAllSelected

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select all series and worklist items
      const newSeriesIds = [...new Set([...checkList.series_ids, ...(seriesIds || [])])]
      const newWorklistIds = [...new Set([...checkList.worklist_ids, ...(worklistIds || [])])]
      updateCheckList?.({
        series_ids: newSeriesIds,
        worklist_ids: newWorklistIds,
      }, switchType)
    } else {
      // Deselect all series and worklist items
      const newSeriesIds = checkList.series_ids.filter((id) => !seriesIds?.includes(id))
      const newWorklistIds = checkList.worklist_ids.filter((id) => !worklistIds?.includes(id))
      updateCheckList?.({
        series_ids: newSeriesIds,
        worklist_ids: newWorklistIds,
      }, switchType)
    }
  }

  const handleClickWrapper = (type: CheckStateEnum) => {
    if (studyId) {
      startSwitch?.(studyId, type)
    }
  }

  return (
    <Flex component="ul" gap={8} className="icons-btn-group">
      {!isThisStudyInSwitchMode && (
        <>
          <li>
            <Tooltip title={
              <TooltipTitle title="Resend" content={i18n.t('tooltips.worklist_resend')} />
            }
            >
              <Button
                {...baseTriggerProps}
                disabled={disabled}
                icon={<ResendIcon />}
                onClick={() => handleClickWrapper(CheckStateEnum.resend)}
              />
            </Tooltip>
          </li>
          <li>
            <Tooltip title={
              <TooltipTitle title="Download" content={i18n.t('tooltips.worklist_download')} />
            }
            >
              <Button
                {...baseTriggerProps}
                disabled={disabled}
                icon={<DownloadIcon />}
                onClick={() => handleClickWrapper(CheckStateEnum.download)}
              />
            </Tooltip>
          </li>
        </>
      )}
      {isThisStudyInSwitchMode && (
        <Flex component="ul" align="center" gap={8}>
          <li>
            <Checkbox
              checked={isAllSelected}
              indeterminate={isIndeterminate}
              onChange={(e) => handleSelectAll(e.target.checked)}
            />
          </li>
          <li>
            <Trigger
              type={switchType}
              disabled={disabled}
              worklistIds={worklistIds}
              onDownload={() => alert('Download')}
            />
          </li>
          <li>
            <Button
              className="btn gray outline basic-shadow"
              size="small"
              onClick={() => toggleSwitch?.()}
              style={{
                width: 56,
                height: 28,
                margin: '2px 0',
              }}
            ><CancelIcon />
            </Button>
          </li>
        </Flex>
      )}
    </Flex>
  )
}

interface TriggerProps {
  type: CheckStateEnum
  disabled?: boolean
  worklistIds?: number[]
  onDownload?: () => void
}

const triggerProps = {
  icon: null,
  className: 'btn outline',
  color: 'primary' as const,
  variant: 'outlined' as const,
  style: {
    height: 28,
    margin: '2px 0',
  },
}

function Trigger({
  type, disabled, worklistIds, onDownload,
}: TriggerProps) {
  if (type === 'resend') {
    return (
      <ResendTrigger
        disabled={disabled}
        worklistIds={worklistIds}
        triggerProps={{ ...triggerProps, children: 'Resend' }}
      />
    )
  }
  if (type === 'download') {
    return (
      <Button
        disabled={disabled}
        {...triggerProps}
        onClick={onDownload}
      >
        {i18n.t('buttons.download')}
      </Button>

    )
  }
  return null
}

export default StudyButtonGroup
