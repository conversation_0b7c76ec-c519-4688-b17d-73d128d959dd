import { Checkbox, Flex } from 'antd'
import { useNavigate } from 'react-router'

import { WorkStatusEnum } from 'src/utils/enum'

import PreviewTrigger from '../trigger/PreviewTrigger'
import ProcessStructureTrigger from '../trigger/ProcessStructureTrigger'
import RedrawTrigger from '../trigger/RedrawTrigger'

interface Props {
  worklistId?: number
  seriesInfo?: PreviewSeriesInfoType
  status?: string
  isSwitch?: boolean
  isPreview?: boolean
  redrawDisabled?: boolean
  onCheckChange?: (checked: boolean, worklistId?: number) => void
  checkList?: {
    series_ids: string[]
    worklist_ids: number[]
  }
  clearCheckList?: () => void
}

function WorklistButtonGroup({
  worklistId,
  seriesInfo,
  status = WorkStatusEnum.SUCCEEDED,
  isSwitch,
  isPreview = true,
  redrawDisabled = false,
  onCheckChange,
  checkList = { series_ids: [], worklist_ids: [] },
  clearCheckList,
}: Props) {
  const navigate = useNavigate()
  const validStatuses = [WorkStatusEnum.SUCCEEDED, WorkStatusEnum.FAILED] as string[]
  const displayProcessStructure = validStatuses.includes(status)

  // Check if this worklist is selected
  const isChecked = worklistId ? checkList.worklist_ids.includes(worklistId) : false

  const handleRedraw = () => {
    clearCheckList?.()
    navigate(`${worklistId}`)
  }

  const handleProcessStructure = () => {
    clearCheckList?.()
    navigate(`/rs/${worklistId}`)
  }

  return (
    <Flex component="nav" gap={8} className="icons-btn-group">
      {!isSwitch && (
        <>
          <PreviewTrigger
            disabled={!isPreview}
            worklistId={worklistId}
            seriesInfo={seriesInfo}
          />
          {/* Redraw */}
          <RedrawTrigger
            disabled={redrawDisabled}
            onClick={handleRedraw}
          />

          {/* Process Structures */}
          {
            displayProcessStructure && (
              <ProcessStructureTrigger onClick={handleProcessStructure} />
            )
          }
        </>
      )}
      {
        isSwitch && (
          <div style={{ width: 152 }}>
            <Checkbox
              checked={isChecked}
              onChange={(e) => onCheckChange?.(e.target.checked, worklistId)}
            />
          </div>
        )
      }
    </Flex>
  )
}

export default WorklistButtonGroup
