import { Divider, Flex } from 'antd'
import { isArray, join } from 'lodash-es'

import { WorkStatusEnum } from 'src/utils/enum'
import { withoutSecond } from 'src/utils/helper'

import SeriesButtonGroup from './SeriesButtonGroup'
import StudyButtonGroup from './StudyButtonGroup'
import WorklistButtonGroup from './WorklistButtonGroup'
import { type CheckListResult } from '../../hooks/useCheckListReducer'
import { TreeDataType } from '../../types'

interface Props extends TreeDataType, CheckListResult {
  patientId: string | null
}

function OperationButtonGroup({
  patientId = '', status = WorkStatusEnum.SUCCEEDED, level, children,
  isSwitch, checkList, switchType, toggleSwitch, updateCheckList, clearCheckList, activeStudyId, startSwitch,
  ...record
}: Props) {
  const seriesInfo = {
    patientID: patientId,
    studyDate: record.study_date || '',
    series: record.series_number || 0,
    seriesDescription: record.series_description || '',
  }

  // Study level (main)
  if (level === 'study') {
    const seriesItems = children?.filter((item) => item !== 'no rs') as TreeDataType[]
    const seriesIds = seriesItems
      ?.map((item: TreeDataType) => item.viewer_series_id)
      .filter((id): id is string => id !== undefined)

    const worklistIds = seriesItems
      ?.flatMap((item: TreeDataType) => item.children || [])
      .filter((child): child is TreeDataType => child !== 'no rs')
      .map((child: TreeDataType) => child.worklist_id)
      .filter((id): id is number => id !== undefined)

    return (
      <Flex
        gap={8}
        align="center"
        className="study-row-operation"
      >
        <StudyButtonGroup
          worklistIds={worklistIds || []}
          seriesIds={seriesIds || []}
          status={WorkStatusEnum.SUCCEEDED}
          studyId={(record as TreeDataType & { viewer_study_id?: string }).viewer_study_id}
          isSwitch={isSwitch}
          checkList={checkList}
          switchType={switchType}
          activeStudyId={activeStudyId}
          updateCheckList={updateCheckList}
          clearCheckList={clearCheckList}
          toggleSwitch={toggleSwitch}
          startSwitch={startSwitch}
        />
        <span>{withoutSecond(record.study_date || '')}</span>
        <Divider type="vertical" style={{ margin: 0 }} />
        <span>
          {isArray(record.modality) ? join(record.modality, ', ') : record.modality}
        </span>
        <Divider type="vertical" style={{ margin: 0 }} />
        <span>{record.study_description}</span>
      </Flex>
    )
  }

  // Series level
  if (level === 'series') {
    // Check if this series belongs to the active study in switch mode
    const isThisSeriesInSwitchMode = isSwitch && activeStudyId === record.viewer_study_id

    return (
      <div
        key={`${record.key}-operation`}
        style={{ height: '100%', paddingLeft: 32 }}
      >
        <SeriesButtonGroup
          worklistId={record.worklist_id}
          checkList={checkList}
          seriesInfo={seriesInfo}
          status={status}
          isSwitch={isThisSeriesInSwitchMode}
          seriesId={record.viewer_series_id}
          clearCheckList={clearCheckList}
          onCheckChange={(checked, seriesId) => {
            // Handle series selection logic here
            if (checked && seriesId) {
              updateCheckList?.({
                series_ids: [...checkList.series_ids, seriesId],
                worklist_ids: checkList.worklist_ids,
              }, switchType)
            } else if (seriesId) {
              updateCheckList?.({
                series_ids: checkList.series_ids.filter((id) => id !== seriesId),
                worklist_ids: checkList.worklist_ids,
              }, switchType)
            }
          }}
        />
      </div>
    )
  }

  const key = typeof record === 'string' ? record : record.key as string

  // Worklist level (default)
  // Check if this worklist belongs to the active study in switch mode
  const isThisWorklistInSwitchMode = isSwitch && activeStudyId === record.viewer_study_id

  return (
    <div key={`${key}-operation`} style={{ paddingLeft: 32 }}>
      <WorklistButtonGroup
        worklistId={record.worklist_id}
        seriesInfo={seriesInfo}
        status={status}
        isSwitch={isThisWorklistInSwitchMode}
        isPreview
        redrawDisabled
        checkList={checkList}
        clearCheckList={clearCheckList}
        onCheckChange={(checked, worklistId) => {
          // Handle worklist selection logic here
          if (checked && worklistId) {
            updateCheckList?.({
              series_ids: checkList.series_ids,
              worklist_ids: [...checkList.worklist_ids, worklistId],
            }, switchType)
          } else if (worklistId) {
            updateCheckList?.({
              series_ids: checkList.series_ids,
              worklist_ids: checkList.worklist_ids.filter((id) => id !== worklistId),
            }, switchType)
          }
        }}
      />
    </div>
  )
}

export default OperationButtonGroup
