import { DownIcon } from 'src/assets/icons'

interface ExpandIconProps {
  expanded: boolean
  onExpand: (record: any, event: React.MouseEvent<HTMLElement>) => void
  record: any
}

function ExpandIcon({ expanded, onExpand, record }: ExpandIconProps) {
  if (record?.level === 'worklist' || record === 'no rs') return null

  return (
    <DownIcon
      style={{
        position: 'absolute',
        zIndex: 10,
        top: '50%',
        left: '16px',
        transform: expanded ? 'rotate(0deg)' : 'rotate(-90deg)',
        translate: '0 -50%',
        transition: 'transform 0.2s',
        cursor: 'pointer',
        color: 'var(--color-gray_0)',
      }}
      onClick={(event) => onExpand(record, event as unknown as React.MouseEvent<HTMLElement>)}
    />
  )
}

export default ExpandIcon
export type { ExpandIconProps }
