import {
  Divider, Flex, Switch, Table,
} from 'antd'
import type { SorterResult } from 'antd/es/table/interface'

import i18n from '@/i18n'
import SubTableLayout from '@/layouts/SubTableLayout'
import { LoadingIcon2 } from 'src/assets/icons'
import { useGetPatientHistoryQuery } from 'src/services/api'
import { useAppSelector } from 'src/store/hook'
import { color } from 'src/utils/variables'

import ExpandIcon from './ExpandIcon'
import { useTreeTableColumns } from '../hooks/useTreeTableColumns'
import { useTreeTableData } from '../hooks/useTreeTableData'
import { TreeDataType } from '../types'

import 'src/styles/components/HistoryWorklistTable.css'

function TreeTable() {
  const { historyFocus } = useAppSelector((state) => state.historyReducer)

  const { data, isLoading } = useGetPatientHistoryQuery({ patient_id: `${historyFocus ?? ''}` })

  // custom hooks
  const { treeColumns } = useTreeTableColumns(historyFocus)
  const { treeData } = useTreeTableData(data || { study: [] })

  const handleSwitchChange = (checked: boolean) => {
    // Handle switch change logic here
  }

  return (
    <SubTableLayout
      theme={{
        components: {
          Table: {
            cellPaddingBlock: 16,
            cellPaddingInline: 8,
            headerBg: color.gray[3],
            colorBgContainer: color.gray[3],
          },
        },
      }}
    >
      <Flex style={{ padding: '.5rem 1.5rem' }} gap={8} justify="flex-end" align="center">
        <label htmlFor="show-records-without-rs">{i18n.t('label.show_records_without_rs')}</label>
        <Switch id="show-records-without-rs" onChange={handleSwitchChange} />
      </Flex>
      <Divider style={{ margin: '0' }} />
      <Table<TreeDataType>
        id="history-worklist-table"
        key={historyFocus}
        columns={treeColumns}
        dataSource={treeData}
        loading={{
          spinning: isLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
        pagination={false}
        scroll={{ x: 'calc(100% - 12px)', y: 'calc(100% - 64px)' }}
        style={{ height: 'calc(100% - 32px)' }}
        onChange={(_pagination, _filters, sorter) => {
          console.log(sorter)
          // setSortedInfo(sorter as SorterResult<HistoryWorklistType>)
        }}
        expandable={{
          expandIcon: ExpandIcon,
          indentSize: 0,
        }}
        rowClassName={(record) => {
          switch (record.level) {
            case 'study':
              return 'study-row'
            case 'series':
              return 'series-row'
            case 'worklist':
              return 'instance-row'
            default:
              return 'no-rs-row'
          }
        }}
      />
    </SubTableLayout>
  )
}

export default TreeTable
