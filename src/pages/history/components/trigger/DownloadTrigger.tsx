import { useState } from 'react'

import { <PERSON><PERSON>, Modal } from 'antd'

import { DownloadIcon } from 'src/assets/icons'
import TransferSwitchGroup from 'src/components/Transfer/TransferSwitchGroup'
import { type SwitchGroup } from 'src/components/Transfer/TransferSwitchGroup'
import useAlert from 'src/hooks/useAlert'
import { TriggerProps, useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useGetDicomMutation } from 'src/services/api'
import { color } from 'src/utils/variables'

interface Props {
  disabled?: boolean
  worklistId?: number
  worklistIds?: number[]
  triggerProps?: TriggerProps<unknown>
}

const initalDownloadSwitchState = {
  rs_transfer: true,
  ct_transfer: false,
}

function DownloadTrigger({
  disabled = false,
  worklistId,
  worklistIds,
  triggerProps,
}: Props) {
  const [downloadSwitch, setDownloadSwitch] = useState<SwitchGroup>(initalDownloadSwitchState)
  const [getDicomMutation] = useGetDicomMutation()
  const handleAlert = useAlert()

  // Determine the ID to use for download
  const downloadId = worklistId || (worklistIds && worklistIds.length > 0 ? worklistIds[0] : undefined)

  // handle modal ok
  const handleDownloadData = async () => {
    if (!downloadId) {
      throw new Error('worklistId not found')
    }
    try {
      await getDicomMutation({
        id: downloadId,
        rs: downloadSwitch.rs_transfer,
        ct: downloadSwitch.ct_transfer,
      }).unwrap()
    } catch (e) {
      handleAlert({ title: i18n.t('error_titles.download'), content: (e as Err).data?.detail }, 'Msg', 'error')
      throw new Error(i18n.t('error_titles.download'))
    } finally {
      setDownloadSwitch(initalDownloadSwitchState)
    }
  }

  const downloadModal = useAntModal({
    triggerProps: {
      className: 'basic-shadow icon-only',
      icon: <DownloadIcon />,
      ...triggerProps,
    },
    modalProps: {
      onOk: handleDownloadData,
      onCancel: () => setDownloadSwitch(initalDownloadSwitchState),
      title: i18n.t('buttons.download'),
      okText: i18n.t('buttons.download'),
      confirmLoading: true,
      destroyOnClose: true,
      styles: {
        body: { padding: '1.5rem 2rem' },
      },
    },
  })

  return (
    <>
      <Button
        disabled={disabled}
        {...downloadModal.triggerProps}
      />
      {/* Download Modal */}
      <Modal
        {...downloadModal.modalProps}
        okButtonProps={{
          variant: 'outlined',
          color: 'primary',
          disabled: !Object.values(downloadSwitch).includes(true),
        }}
      >
        <p style={{ lineHeight: '2.25rem' }}>{i18n.t('descriptions.download')}</p>
        <TransferSwitchGroup
          checked={downloadSwitch}
          onChange={setDownloadSwitch}
          groupText={{
            rs: `${i18n.t('buttons.download')} ${i18n.t('titles.rs')} `,
            images: `${i18n.t('buttons.download')} ${i18n.t('titles.image')}`,
          }}
          style={{
            padding: 16,
            marginLeft: 0,
            marginTop: 8,
            background: color.gray[4],
          }}
          styles={{
            text: { flex: '0 0 132px' },
          }}
        />
      </Modal>
    </>
  )
}

export default DownloadTrigger
