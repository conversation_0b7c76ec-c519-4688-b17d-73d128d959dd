import React from 'react'

import { <PERSON><PERSON>, type ButtonProps, Tooltip } from 'antd'

import { PenToolIcon } from '@/assets/icons'
import TooltipTitle from '@/components/TooltipTitle'
import i18n from '@/i18n'

import { baseTriggerProps } from 'src/utils/saveButton'

function RedrawTrigger({ ...props }: ButtonProps) {
  return (
    <Tooltip title={<TooltipTitle title="Redraw" content={i18n.t('tooltips.worklist_redraw')} />}>
      <Button
        {...baseTriggerProps}
        icon={<PenToolIcon style={{ fontSize: 24 }} />}
        {...props}
      />
    </Tooltip>
  )
}

export default RedrawTrigger
