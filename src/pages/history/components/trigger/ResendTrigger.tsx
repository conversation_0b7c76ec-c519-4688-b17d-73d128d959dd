import { Button, Modal } from 'antd'

import { ResendIcon } from 'src/assets/icons'
import TransferTabs from 'src/components/Transfer/TransferTabs'
import useAlert from 'src/hooks/useAlert'
import { TriggerProps, useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useGetRemoteConfigMutation, usePostRsResendMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { clearResendRemote, updateResendRemote } from 'src/store/reducers/worklistSlice'
import { baseTriggerProps } from 'src/utils/saveButton'
import { verifyDetailState } from 'src/utils/verify'

import 'src/styles/components/modal.css'

interface Props {
  disabled?: boolean
  worklistId?: number
  worklistIds?: number[]
  triggerProps?: TriggerProps<unknown>
}

function ResendTrigger({
  disabled = false,
  worklistId,
  worklistIds,
  triggerProps,
}: Props) {
  // state
  const { resendRemote } = useAppSelector((state) => state.worklistReducer)
  const dispatch = useAppDispatch()
  const chooseCount = resendRemote.destination.folder.length + resendRemote.destination.remote_server.length

  // hook
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [postRsResendMutation] = usePostRsResendMutation()
  const handleAlert = useAlert()

  // Determine the ID to use for resend
  const resendId = worklistId || (worklistIds && worklistIds.length > 0 ? worklistIds[0] : undefined)

  // modal
  const resendModal = useAntModal({
    triggerProps: {
      ...baseTriggerProps,
      className: 'icon-only',
      icon: <ResendIcon />,
      onClick: async () => {
        try {
          await getRemoteConfigMutation().unwrap()
        } catch (e) {
          handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
        }
      },
      ...triggerProps,
    },
    modalProps: {
      onCancel: async () => {
        dispatch(clearResendRemote())
      },
      async onOk(_, setConfirmLoading) {
        if (!resendId || chooseCount === 0) {
          dispatch(clearResendRemote())
          setConfirmLoading(false)
          throw new Error('Choose at least one destination')
        }
        try {
          await postRsResendMutation({
            id: resendId,
            remote_server_id_list: resendRemote.destination.remote_server,
            folder_id_list: resendRemote.destination.folder,
          }).unwrap()
          dispatch(clearResendRemote())
        } catch (e) {
          handleAlert({ title: i18n.t('error_titles.resend'), content: (e as Err).data?.detail }, 'Msg', 'error')
          throw new Error((e as Err).data?.detail)
        }
      },
      okText: i18n.t('buttons.send'),
      className: 'resend-modal',
      styles: {
        body: { padding: '3rem 3.75rem' },
      },
      width: '62.5rem',
      confirmLoading: true,
    },
  })

  // function
  const handleUpdateWorklistRemote = (
    type: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[],
  ) => dispatch(updateResendRemote({ type, remote, value }))

  return (
    <>
      <Button
        disabled={disabled}
        {...resendModal.triggerProps}
      />
      {/* Resend Modal */}
      <Modal
        {...resendModal.modalProps}
        okButtonProps={{
          variant: 'outlined',
          color: 'primary',
          disabled: verifyDetailState.destination(resendRemote.destination) || chooseCount === 0,
        }}
      >
        <hgroup>
          <h3 style={{ fontSize: 24 }}>{i18n.t('titles.resend_destination')}</h3>
          <p>
            {i18n.t('paragraphs.choose_destination', { chooseCount, joinArrays: ' ' })}
          </p>
        </hgroup>
        <TransferTabs
          type="destination"
          checkList={resendRemote}
          onUpdateRemote={handleUpdateWorklistRemote}
        />
      </Modal>
    </>
  )
}

export default ResendTrigger
