import React from 'react'

import { <PERSON><PERSON>, type ButtonProps, Tooltip } from 'antd'

import { BooleanIcon } from '@/assets/icons'
import TooltipTitle from '@/components/TooltipTitle'
import i18n from '@/i18n'

import { baseTriggerProps } from 'src/utils/saveButton'

function ProcessStructureTrigger({ ...props }: ButtonProps) {
  return (
    <Tooltip
      title={<TooltipTitle title="Process Structures" content={i18n.t('tooltips.process_structures')} />}
    >
      <Button
        {...baseTriggerProps}
        icon={<BooleanIcon style={{ fontSize: 18 }} />}
        {...props}
      />
    </Tooltip>
  )
}

export default ProcessStructureTrigger
