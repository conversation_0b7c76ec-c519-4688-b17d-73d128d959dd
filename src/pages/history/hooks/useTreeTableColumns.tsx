import { useEffect } from 'react'

import type { TableColumnsType } from 'antd'

import { EnterIcon } from 'src/assets/icons'
import i18n from 'src/i18n'

import OperationButtonGroup from '../components/button-group/OperationButtonGroup'
import { TreeDataType } from '../types'
import { useCheckListReducer } from './useCheckListReducer'

function getIsFullRow(record: TreeDataType) {
  const isStudy = typeof record === 'object' && record.level === 'study'
  const noRs = typeof record === 'string' && record === 'no rs'
  return isStudy || noRs
}

const onCell = (record: TreeDataType) => ({
  colSpan: getIsFullRow(record) ? 0 : 1,
})

function RenderCell({
  record, index, minWidth = 100,
}: {
  record: TreeDataType
  index: keyof Omit<TreeDataType, 'children' | 'key'>
  minWidth?: number
}) {
  if (record.level === 'study') return null
  const value = record[index]

  return <div style={{ minWidth }}>{value ?? '-'}</div>
}

export function useTreeTableColumns(patientId: null | string = '') {
  const {
    checkListState, toggleSwitch, updateCheckList, clearCheckList, startSwitch,
  } = useCheckListReducer()

  // Tree Table Columns
  const treeColumns: TableColumnsType<TreeDataType> = [
    // 1. Operation
    {
      title: i18n.t('titles.operation'),
      key: 'operation',
      width: 210,
      onCell: (record) => ({
        colSpan: getIsFullRow(record) ? 9 : 1,
      }),
      render: (_, record) => {
        const noRs = typeof record === 'string' && record === 'no rs'
        if (noRs) {
          return (
            <div style={{ color: 'var(--color-gray_1)' }}>No RS</div>
          )
        }

        return (
          <OperationButtonGroup
            patientId={patientId}
            toggleSwitch={toggleSwitch}
            updateCheckList={updateCheckList}
            clearCheckList={clearCheckList}
            isSwitch={checkListState.isSwitch}
            checkList={checkListState.checkList}
            switchType={checkListState.type}
            activeStudyId={checkListState.activeStudyId}
            startSwitch={startSwitch}
            {...record}
          />
        )
      },
    },
    // 2. Modality
    {
      title: 'Modality',
      key: 'modality_header',
      width: 80,
      onCell,
      render: (_, record) => {
        if (record.level === 'study') return null

        let modalityValue: React.ReactNode = '-'
        if (record.level === 'series') {
          modalityValue = 'CT'
        } else if (record.level === 'worklist') {
          modalityValue = <><EnterIcon />&nbsp;RS</>
        }

        return <div style={{ minWidth: 64 }}>{modalityValue}</div>
      },
    },
    // 3. Images
    {
      title: 'Images',
      key: 'images_header',
      width: 70,
      onCell,
      render: (_, record) => <RenderCell record={record} index="images" minWidth={54} />,
    },
    // 4. RS Set Label (protocol)
    {
      title: 'RS Set Label',
      key: 'rs_set_label_header',
      width: 160,
      onCell,
      render: (_, record) => <RenderCell record={record} index="protocol" minWidth={144} />,
    },
    // 5. Series Number
    {
      title: 'Series Number',
      key: 'series_number_header',
      width: 110,
      onCell,
      render: (_, record) => <RenderCell record={record} index="series_number" minWidth={94} />,
    },
    // 6. Series Description
    {
      title: 'Series Description',
      key: 'series_description_header',
      width: 150,
      onCell,
      render: (_, record) => <RenderCell record={record} index="series_description" minWidth={134} />,
    },
    // 7. Source
    {
      title: 'Source',
      key: 'source_header',
      width: 100,
      onCell,
      render: (_, record) => <RenderCell record={record} index="source" minWidth={84} />,
    },
    // 8. Status
    {
      title: 'Status',
      key: 'status_header',
      width: 100,
      onCell,
      render: (_, record) => <RenderCell record={record} index="status" minWidth={84} />,
    },
    // 9. Last Modified
    {
      title: 'Last Modified',
      key: 'last_modified_header',
      width: 120,
      onCell,
      render: (_, record) => <RenderCell record={record} index="last_modified" minWidth={104} />,
    },
  ]

  useEffect(() => { return () => clearCheckList() }, [patientId])

  return {
    treeColumns,
  }
}
