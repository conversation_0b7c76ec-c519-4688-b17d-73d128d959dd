import { TreeDataType } from '../types'

export function useTreeTableData(apiData: HistoryWorklistResponseType) {
  if (!apiData?.study) return { treeData: [] }

  const treeData: TreeDataType[] = apiData?.study.map((studyItem, studyIndex) => {
    const studyNode = {
      key: `study-${studyIndex}`,
      viewer_study_id: studyItem.viewer_study_id,
      study_date: studyItem.study_date,
      study_description: studyItem.study_description,
      modality: studyItem.total_modality,
      level: 'study' as const,
      // series row
      children: studyItem.series?.map((seriesItem) => {
        const seriesNode = {
          key: `series-${seriesItem.series_number}-${seriesItem.series_number}`,
          viewer_study_id: studyItem.viewer_study_id,
          viewer_series_id: seriesItem.viewer_series_id,
          series_number: seriesItem.series_number,
          series_description: seriesItem.series_description,
          instance_count: seriesItem.instance_count,
          // Add default values for columns
          source: 'DICOM Server',
          last_modified: new Date().toISOString(),
          level: 'series' as const,
          // worklist row
          children: seriesItem.worklist && seriesItem.worklist.length > 0
            ? seriesItem.worklist.map((worklistItem) => ({
              key: `worklist-${studyIndex}-${seriesItem.series_number}-${worklistItem.id}`,
              viewer_study_id: studyItem.viewer_study_id,
              worklist_id: worklistItem.id,
              images: worklistItem.image_count,
              status: worklistItem.status.toString(),
              protocol: worklistItem.protocol,
              // Add default values for columns
              source: 'DICOM Server',
              last_modified: new Date().toISOString(),
              level: 'worklist' as const,
            }))
            : ['no rs'] as (TreeDataType | 'no rs')[],
        }
        return seriesNode
      }) || [],
    }
    return studyNode
  })

  return {
    treeData,
  }
}
