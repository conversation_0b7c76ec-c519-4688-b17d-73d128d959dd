import { WorklistGroupTableProps } from 'src/components/Table/WorklistGroupTable'
import { useGetHistoryQuery } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { setHistoryFocus, setCurrentPage, setSorter } from 'src/store/reducers/historySlice'

import { historyColumns } from '../column'

export function useHistoryManager(): WorklistGroupTableProps<WorklistGroupType, 'patient_id'> {
  const dispatch = useAppDispatch()
  const {
    historyFocus, searchParameter, sorter, pagination,
  } = useAppSelector((state) => state.historyReducer)

  const { data, isLoading } = useGetHistoryQuery({
    patient_id: searchParameter.patientId,
    study_date_range_start: searchParameter.studyDateRangeStart,
    study_date_range_end: searchParameter.studyDateRangeEnd,
    order_key: sorter.orderKey,
    ascend: sorter.ascend,
    page: pagination.current,
  })

  const handleSetRowFocus = (patientId: string) => {
    dispatch(setHistoryFocus(patientId))
  }

  const handleSortChange = (orderKey: string, ascend: boolean) => {
    dispatch(setSorter({ orderKey, ascend }))
  }

  const handlePageChange = (currentPage?: number) => {
    dispatch(setCurrentPage(currentPage))
  }

  return {
    columns: historyColumns,
    data: data?.history || [],
    // pagination
    pagination: {
      current: pagination.current,
      total: pagination.total,
      pageSize: pagination.pageSize,
    },
    // loading
    isLoading,
    // focus
    rowFocus: historyFocus,
    rowFocusKey: 'patient_id',
    // function
    handleSetRowFocus,
    handleSortChange,
    handlePageChange,
  }
}
