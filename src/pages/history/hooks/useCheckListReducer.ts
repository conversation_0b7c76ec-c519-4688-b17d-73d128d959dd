import { useReducer } from 'react'

enum CheckStateEnum {
  resend = 'resend',
  download = 'download',
  none = '',
}

type CheckListState = {
  checkList: {
    series_ids: string[]
    worklist_ids: number[]
  }
  type: CheckStateEnum
  isSwitch: boolean
  activeStudyId?: string // Track which study (by viewer_study_id) is currently in switch mode
}

type UpdateCheckListAction = {
  type: 'update'
  payload: CheckListState
}

type StartSwitchAction = {
  type: 'startSwitch'
  payload: {
    studyId: string
    switchType: CheckStateEnum
  }
}

type ClearCheckListAction = {
  type: 'clear'
}

type ToggleSwitchAction = {
  type: 'toggleSwitch'
  payload?: boolean
}

type CheckListAction = ClearCheckListAction | UpdateCheckListAction | ToggleSwitchAction | StartSwitchAction

function reducer(state: CheckListState, action: CheckListAction): CheckListState {
  switch (action.type) {
    case 'update':
      return { ...state, ...action.payload }
    case 'startSwitch':
      return {
        ...state,
        checkList: { series_ids: [], worklist_ids: [] },
        type: action.payload.switchType,
        isSwitch: true,
        activeStudyId: action.payload.studyId,
      }
    case 'clear':
      return {
        ...state,
        checkList: { series_ids: [], worklist_ids: [] },
        type: CheckStateEnum.none,
        isSwitch: false,
        activeStudyId: undefined,
      }
    case 'toggleSwitch': {
      const isSwitch = action.payload ?? !state.isSwitch
      return {
        ...state,
        type: !isSwitch ? CheckStateEnum.none : state.type,
        checkList: !isSwitch ? { series_ids: [], worklist_ids: [] } : state.checkList,
        isSwitch,
        activeStudyId: !isSwitch ? undefined : state.activeStudyId,
      }
    }
    default:
      return state
  }
}

const useCheckListReducer = () => {
  const [checkListState, dispatchCheckList] = useReducer(reducer, {
    checkList: { series_ids: [], worklist_ids: [] },
    type: CheckStateEnum.none,
    isSwitch: false,
    activeStudyId: undefined,
  })

  const updateCheckList = (checkList: { series_ids: string[], worklist_ids: number[] }, type: CheckStateEnum) => {
    dispatchCheckList({
      type: 'update',
      payload: {
        checkList,
        type,
        isSwitch: true,
        activeStudyId: checkListState.activeStudyId,
      },
    })
  }

  const startSwitch = (studyId: string, switchType: CheckStateEnum) => {
    dispatchCheckList({ type: 'startSwitch', payload: { studyId, switchType } })
  }

  const clearCheckList = () => {
    dispatchCheckList({ type: 'clear' })
  }

  const toggleSwitch = (payload?: boolean) => {
    dispatchCheckList({ type: 'toggleSwitch', payload })
  }

  return {
    checkListState,
    updateCheckList,
    clearCheckList,
    toggleSwitch,
    startSwitch,
  }
}

interface CheckListResult {
  checkList: {
    series_ids: string[]
    worklist_ids: number[]
  }
  isSwitch: boolean
  switchType: CheckStateEnum
  activeStudyId?: string
  toggleSwitch?: (payload?: boolean) => void
  updateCheckList?: (checkList: { series_ids: string[], worklist_ids: number[] }, type: CheckStateEnum) => void
  clearCheckList?: () => void
  startSwitch?: (studyId: string, switchType: CheckStateEnum) => void
}

export { useCheckListReducer, CheckStateEnum }
export type { CheckListState, CheckListResult }
