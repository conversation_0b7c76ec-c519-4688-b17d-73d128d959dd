import { Form } from 'antd'

import WorklistLayout from 'src/layouts/WorklistLayout'
import { useAppDispatch } from 'src/store/hook'
import { setSearchParameter } from 'src/store/reducers/historySlice'

import TreeTable from './components/TreeTable'
import { useHistoryManager } from './hooks/useHistoryManager'
import { HISTORY_OPTIONS } from './utils'

function HistoryPage() {
  const [form] = Form.useForm()
  const dispatch = useAppDispatch()

  const historyManager = useHistoryManager()

  const handleSearch = () => {
    const values = form.getFieldsValue()
    dispatch(setSearchParameter({
      patientId: values.patientId,
      studyStatus: values.studyStatus,
      studyDateRangeStart: values.pickDate?.[0].format('YYYY-MM-DD'),
      studyDateRangeEnd: values.pickDate?.[1].format('YYYY-MM-DD'),
    }))
  }

  return (
    <WorklistLayout
      isHistoryPage
      form={form}
      pageTitle="titles.history"
      searchOptions={HISTORY_OPTIONS}
      onSearch={handleSearch}
      tableController={historyManager}
    >
      <TreeTable />
    </WorklistLayout>
  )
}

export default HistoryPage
