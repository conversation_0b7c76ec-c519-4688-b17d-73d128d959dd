export interface TreeDataType {
  key: React.Key
  // Study level (main)
  viewer_study_id?: string
  study_date?: string
  study_description?: string
  modality?: ModalityType[]
  // Series level (group)
  viewer_series_id?: string
  series_number?: number
  series_description?: string
  instance_count?: number
  // Worklist level (item)
  worklist_id?: number
  images?: number
  status?: string
  protocol?: number
  // Additional fields for 9 columns
  source?: string
  last_modified?: string
  level: 'study' | 'series' | 'worklist'
  children?: (TreeDataType | 'no rs')[]
}
