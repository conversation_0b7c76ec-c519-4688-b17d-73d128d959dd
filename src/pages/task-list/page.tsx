import { Form } from 'antd'
import { useNavigate } from 'react-router'

import WorklistLayout from 'src/layouts/WorklistLayout'
import { useAppDispatch } from 'src/store/hook'
import { setSearchParameter } from 'src/store/reducers/taskListSlice'

import TaskTable from './components/TaskTable'
import { useTaskListActions } from './hooks/useTaskListActions'
import { useTaskListManager } from './hooks/useTaskListManager'
import { STATUS_OPTIONS } from './utils'

function TaskListPage() {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()

  const taskListManager = useTaskListManager()
  const selectTaskGroup = taskListManager.data.find((item) => item.id === taskListManager.rowFocus)

  const {
    handleSwitchTaskStatus,
    handleRemoveTaskGroup,
    handleSuspendTaskGroup,
    handlePrioritizeTaskGroup,
  } = useTaskListActions()

  const handleSearch = () => {
    const values = form.getFieldsValue()
    dispatch(setSearchParameter({
      patientId: values.patientId,
      studyStatus: values.studyStatus,
      studyDateRangeStart: values.pickDate?.[0].format('YYYY-MM-DD'),
      studyDateRangeEnd: values.pickDate?.[1].format('YYYY-MM-DD'),
    }))
  }

  const handleNavigateToTaskPage = (taskId: number) => navigate(`/worklist/${taskId}`)

  return (
    // Task Group List
    <WorklistLayout
      form={form}
      pageTitle="titles.task_list"
      searchOptions={STATUS_OPTIONS}
      onSearch={handleSearch}
      tableController={taskListManager}
    >
      <TaskTable
        taskGroup={selectTaskGroup}
        onNavigateToTaskPage={handleNavigateToTaskPage}
        onSwitchTaskStatus={handleSwitchTaskStatus}
        onRemoveTaskGroup={handleRemoveTaskGroup}
        onSuspendTaskGroup={handleSuspendTaskGroup}
        onPrioritizeTaskGroup={handlePrioritizeTaskGroup}
      />
    </WorklistLayout>
  )
}

export default TaskListPage
