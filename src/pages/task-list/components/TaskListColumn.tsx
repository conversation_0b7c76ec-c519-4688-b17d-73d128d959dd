import type { ColumnsType } from 'antd/es/table/interface'

import { PriorityIcon } from 'src/assets/icons'
import ProgressBar from 'src/components/ProgressBar'
import i18n from 'src/i18n'
import { WorkStatusEnum } from 'src/utils/enum'
import { formattedStatus, withoutTime } from 'src/utils/helper'
import { color } from 'src/utils/variables'

export const worklistColumns: ColumnsType<WorklistGroupType> = [
  {
    dataIndex: 'insert',
    key: 'insert',
    width: '25px',
    render: (_, { insert }) => (
      <span>
        {insert && (
          <div className="icon-box">
            <PriorityIcon width={24} />
          </div>
        )}
      </span>
    ),
  },
  {
    title: i18n.t('titles.patient_id'),
    dataIndex: 'patient_id',
    key: 'patient_id',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    ellipsis: true,
  },
  {
    title: i18n.t('titles.study_date'),
    dataIndex: 'study_date',
    key: 'study_date',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    render: (_, { study_date }) => (
      <p style={{
        minWidth: 90,
        color: color.gray[1].default,
      }}
      >
        {withoutTime(study_date)}
      </p>
    ),
  },
  {
    title: i18n.t('titles.study_status'),
    dataIndex: 'study_status',
    key: 'study_status',
    width: '100px',
    sorter: false,
    showSorterTooltip: false,
    render: (value, { progress }) => {
      const studyStatus: string[] = [WorkStatusEnum.PROCESSING, WorkStatusEnum.PENDING, WorkStatusEnum.SUSPENDED]
      if (studyStatus.includes(value)) {
        return (
          <ProgressBar
            status={value}
            progress={progress * 100}
          />
        )
      }
      return (
        <div className="progress-title">
          {formattedStatus(value)}
        </div>
      )
    },
  },
]
