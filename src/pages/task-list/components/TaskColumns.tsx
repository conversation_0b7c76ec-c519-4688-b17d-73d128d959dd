import { Flex, Tooltip, Switch } from 'antd'
import type { ColumnsType } from 'antd/es/table/interface'

import { Setting3 } from 'src/assets/icons'
import Button from 'src/components/Button'
import i18n from 'src/i18n'
import { WorkStatusEnum } from 'src/utils/enum'
import { withoutSecond, formattedStatus } from 'src/utils/helper'

interface Props {
  task_status: string
  onNavigateToTaskPage: (taskId: number) => void
  onSwitchTaskStatus: (taskId: number, status: typeof WorkStatusEnum[keyof typeof WorkStatusEnum]) => void
}

export const getTaskColumns = ({
  task_status,
  onNavigateToTaskPage,
  onSwitchTaskStatus,
}: Props): ColumnsType<SeriesType> => {
  const textDisabled = task_status === WorkStatusEnum.SUSPENDED ? 'text-disabled' : ''
  const seriesConfigBtn: string[] = [WorkStatusEnum.PENDING, WorkStatusEnum.CANCELLED]

  return [
    {
      title: i18n.t('titles.operation'),
      dataIndex: 'operation',
      key: 'operation',
      width: '8.75rem',
      render: (_, { worklist_id, status }) => (
        <Flex component="nav" gap={12}>
          <Tooltip title={i18n.t('tooltips.worklist_operation')} color="var(--color-gray_2)">
            <Button
              className="icon-only"
              disabled={!seriesConfigBtn.includes(status)}
              onClick={() => onNavigateToTaskPage(worklist_id)}
            >
              <Setting3 width={20} height={20} />
            </Button>
          </Tooltip>
        </Flex>
      ),
    },
    {
      title: i18n.t('titles.series'),
      dataIndex: 'number',
      key: 'number',
      width: '100px',
      render: (_, { number }) => (
        <span className={textDisabled} style={{ display: 'block', minWidth: '60px', textIndent: '4px' }}>{number}</span>
      ),
    },
    {
      title: i18n.t('titles.series_time'),
      dataIndex: 'series_time',
      key: 'series_time',
      width: '180px',
      render: (_, { time }) => (
        <p className={textDisabled} style={{ whiteSpace: 'nowrap' }}>{withoutSecond(time)}</p>
      ),
    },
    {
      title: i18n.t('titles.last_modified'),
      dataIndex: 'last_modified',
      key: 'last_modified',
      width: '180px',
      render: (_, { last_modified }) => (
        <p className={textDisabled} style={{ whiteSpace: 'nowrap' }}>{withoutSecond(last_modified)}</p>
      ),
    },
    {
      title: i18n.t('titles.image'),
      dataIndex: 'image',
      key: 'image',
      width: '120px',
      render: (_, { image }) => (
        <span className={textDisabled} style={{ display: 'block', minWidth: '60px', textIndent: '4px' }}>{image}</span>
      ),
    },
    {
      title: i18n.t('titles.series_description'),
      dataIndex: 'description',
      key: 'description',
      render: (_, { description }) => (
        <div className={textDisabled} style={{ whiteSpace: 'break-spaces', minWidth: '300px' }}>{description}</div>
      ),
    },
    {
      title: i18n.t('titles.status'),
      dataIndex: 'status',
      key: 'status',
      width: '100px',
      render: (_, { worklist_id, status }) => {
        if (seriesConfigBtn.includes(status)) {
          return (
            <Flex gap={8} align="center" justify="space-between" style={{ width: '115px' }}>
              <span className={`progress-title ${textDisabled}`}>
                {i18n.t(status === WorkStatusEnum.PENDING ? 'switch_options.pending' : 'switch_options.cancelled')}
              </span>
              <Tooltip title={i18n.t('tooltips.worklist_status_switch')}>
                <Switch
                  checked={status === WorkStatusEnum.PENDING}
                  onChange={(bool) => onSwitchTaskStatus(
                    worklist_id,
                    bool ? WorkStatusEnum.PENDING : WorkStatusEnum.CANCELLED,
                  )}
                  style={{ border: '1px solid white' }}
                  disabled={task_status === WorkStatusEnum.SUSPENDED}
                />
              </Tooltip>
            </Flex>
          )
        }
        return (
          <div className={`progress-title ${textDisabled}`}>{formattedStatus(status)}</div>
        )
      },
    },
  ]
}
