import { Table } from 'antd'

import SubTableLayout from '@/layouts/SubTableLayout'
import { LoadingIcon2 } from 'src/assets/icons'
import { useGetWorklistQuery } from 'src/services/api'
import { WorkStatusEnum } from 'src/utils/enum'
import { color } from 'src/utils/variables'

import { getTaskColumns } from './TaskColumns'
import TaskNavbar from './TaskNavbar'

interface Props {
  taskGroup: any,
  onNavigateToTaskPage: (taskId: number) => void
  onSwitchTaskStatus: (taskId: number, status: typeof WorkStatusEnum[keyof typeof WorkStatusEnum]) => void
  onRemoveTaskGroup: (taskGroupId: number) => void,
  onSuspendTaskGroup: (taskGroupId: number, groupStatus: typeof WorkStatusEnum[keyof typeof WorkStatusEnum]) => void,
  onPrioritizeTaskGroup: (taskGroupId: number) => void,
}

function TaskTable({
  taskGroup,
  onNavigateToTaskPage,
  onSwitchTaskStatus,
  onRemoveTaskGroup,
  onSuspendTaskGroup,
  onPrioritizeTaskGroup,
}: Props) {
  const { data, isLoading } = useGetWorklistQuery(
    { worklist_group_id: taskGroup?.id },
    { skip: !taskGroup?.id },
  )
  const task = taskGroup?.id ? data : undefined

  return (
    <SubTableLayout
      theme={{
        components: {
          Table: {
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[2],
            headerBg: color.gray[3],
            headerSortHoverBg: color.gray[2],
            headerSortActiveBg: color.gray[3],
            bodySortBg: color.gray[3],
            colorPrimary: color.primary.default,
          },
        },
      }}
      table={{ className: 'series-table-container' }}
    >
      <TaskNavbar
        taskGroup={taskGroup}
        task={task}
        onRemoveTaskGroup={onRemoveTaskGroup}
        onPrioritizeTaskGroup={onPrioritizeTaskGroup}
        onSuspendTaskGroup={onSuspendTaskGroup}
      />
      <Table
        columns={getTaskColumns({
          task_status: taskGroup?.study_status,
          onNavigateToTaskPage,
          onSwitchTaskStatus,
        })}
        dataSource={task?.series}
        loading={{
          spinning: isLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
        pagination={false}
        rowKey={(record) => record.worklist_id}
        scroll={{ x: true, y: 'calc(100% - 61px)' }}
      />
    </SubTableLayout>
  )
}

export default TaskTable
