import { useEffect, useState } from 'react'

import { <PERSON>lex, Toolt<PERSON> } from 'antd'

import {
  DeleteIcon, PlayIcon, PriorityIcon, StopIcon,
} from 'src/assets/icons'
import Button from 'src/components/Button'
import { ModalBtn } from 'src/components/Modal'
import i18n from 'src/i18n'
import { WorkStatusEnum } from 'src/utils/enum'

const navItem = [
  {
    key: 'Series Count',
    content: 'series_count',
  },
  {
    key: 'Study Description',
    content: 'study_description',
  },
  {
    key: 'Last Modified',
    content: 'last_modified',
  },
]

interface Props {
  taskGroup: any,
  task: any,
  onRemoveTaskGroup: (taskGroupId: number) => void,
  onSuspendTaskGroup: (taskGroupId: number, groupStatus: typeof WorkStatusEnum[keyof typeof WorkStatusEnum]) => void,
  onPrioritizeTaskGroup: (taskGroupId: number) => void,
}

function TaskNavbar({
  taskGroup,
  task,
  onRemoveTaskGroup,
  onSuspendTaskGroup,
  onPrioritizeTaskGroup,
}: Props) {
  const [btnGroup, setBtnGroup] = useState<OperationBtnGroup[]>([
    { isDisabled: true },
    { isDisabled: true },
    { isDisabled: true },
  ])

  const newBtnGroup = (arr: boolean[]) => {
    const newGroup = btnGroup.map((btn, index) => ({
      ...btn, isDisabled: arr[index],
    }))
    setBtnGroup(newGroup)
  }

  useEffect(() => {
    // handle the button able when series change
    if (taskGroup) {
      if (taskGroup.study_status === WorkStatusEnum.PROCESSING) {
        return newBtnGroup([true, true, true])
      }
      if (taskGroup.study_status === WorkStatusEnum.SUSPENDED) {
        return newBtnGroup([true, false, true])
      }
      return newBtnGroup([false, false, false])
    }
    return newBtnGroup([true, true, true])
  }, [taskGroup])

  return (
    <nav className="series-navbar">
      {
        navItem.map((item) => (
          <div key={item.key}>
            <h4 className="series-navbar-title">{item.key}</h4>
            <p
              className="series-navbar-content"
              style={{ color: '#fff' }}
            >
              {task?.[item.content as keyof SeriesNavbar] || ''}
            </p>
          </div>
        ))
      }

      {/* operation button group */}
      <div>
        <h4 className="series-navbar-title">{i18n.t('titles.operation')}</h4>
        <Flex component="nav" gap={12}>
          <ModalBtn
            btnName={(<DeleteIcon />)}
            modalTitle={i18n.t('modal_titles.delete_confirmation')}
            okText={i18n.t('buttons.remove')}
            onOk={() => onRemoveTaskGroup(taskGroup.id)}
            width={350}
            modalBodyStyle={{ padding: '4.75rem 0' }}
            btnClass="icon-only"
            btnDisabled={btnGroup[0].isDisabled}
            tooltip={i18n.t('tooltips.worklist_remove')}
          >
            {i18n.t('modal_contents.delete_confirmation', {
              item: `"${taskGroup?.patient_id}"`,
              joinArrays: ' ',
            })}
          </ModalBtn>
          <Tooltip
            title={
              btnGroup[0].isDisabled
                ? i18n.t('tooltips.worklist_start')
                : i18n.t('tooltips.worklist_suspend')
            }
          >
            <Button
              disabled={btnGroup[1].isDisabled}
              onClick={() => {
                onSuspendTaskGroup(
                  taskGroup.id,
                  taskGroup.study_status === WorkStatusEnum.SUSPENDED
                    ? WorkStatusEnum.PENDING : WorkStatusEnum.SUSPENDED,
                )
              }}
              className="icon-only"
            >
              {taskGroup?.study_status === WorkStatusEnum.SUSPENDED ? <PlayIcon /> : <StopIcon />}
            </Button>
          </Tooltip>
          <Tooltip title={i18n.t('tooltips.worklist_prioritize')}>
            <Button
              disabled={btnGroup[2].isDisabled}
              onClick={() => { onPrioritizeTaskGroup(taskGroup.id) }}
              className="icon-only"
            >
              <PriorityIcon />
            </Button>
          </Tooltip>
        </Flex>
      </div>
    </nav>
  )
}

export default TaskNavbar
