import useAlert from 'src/hooks/useAlert'
import {
  usePatchWorklistStatusMutation,
  usePatchPrioritizeWorklistGroupMutation,
  usePatchWorklistGroupMutation,
} from 'src/services/api'
import { WorkStatusEnum } from 'src/utils/enum'

export function useTaskListActions() {
  const handleAlert = useAlert()

  const [patchWorklistStatusMutation] = usePatchWorklistStatusMutation()
  const [patchPrioritizeWorklistGroupMutation] = usePatchPrioritizeWorklistGroupMutation()
  const [patchWorklistGroupMutation] = usePatchWorklistGroupMutation()

  const handleSwitchTaskStatus = async (
    taskId: number,
    status: typeof WorkStatusEnum[keyof typeof WorkStatusEnum],
  ) => {
    try {
      await patchWorklistStatusMutation({ id: taskId, status }).unwrap()
      // !! GroupFocus !!
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleSuspendTaskGroup = async (
    taskGroupId: number,
    groupStatus: typeof WorkStatusEnum[keyof typeof WorkStatusEnum],
  ) => {
    try {
      await patchWorklistGroupMutation({ worklist_group_id: taskGroupId, status: groupStatus }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleRemoveTaskGroup = async (taskGroupId: number) => {
    try {
      await patchWorklistGroupMutation(
        {
          worklist_group_id: taskGroupId,
          status: WorkStatusEnum.REMOVED,
        },
      ).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handlePrioritizeTaskGroup = async (taskGroupId: number) => {
    try {
      await patchPrioritizeWorklistGroupMutation({ worklist_group_id: taskGroupId }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  return {
    handleSwitchTaskStatus,
    handleRemoveTaskGroup,
    handleSuspendTaskGroup,
    handlePrioritizeTaskGroup,
  }
}
