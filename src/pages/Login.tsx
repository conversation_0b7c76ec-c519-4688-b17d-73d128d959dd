import { useState } from 'react'

import {
  Checkbox, Flex, Form, Input,
  Spin,
} from 'antd'
import { Content } from 'antd/es/layout/layout'

import { LoadingIcon2 } from 'src/assets/icons'
import Button from 'src/components/Button'
import i18n from 'src/i18n'
import { usePostLoginMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { updateRememberAccount } from 'src/store/reducers/authSlice'
import { requiredRules, minLengthRules } from 'src/utils/verify'

import 'src/styles/pages/login.css'

type FieldType = {
  account: string
  password?: string
  remember: boolean
}

const { systemTitle } = window.config.env

function Login() {
  const [form] = Form.useForm()
  // state
  const [errorText, setErrorText] = useState<string | undefined>('')
  const [loading, setLoading] = useState(false)
  const { rememberAccount } = useAppSelector((state) => state.authReducer)
  const dispatch = useAppDispatch()

  // server
  const [postLoginMutation] = usePostLoginMutation()

  const postLogin = async () => {
    const { account, password, remember } = form.getFieldsValue()
    if (form.getFieldError('account').length || form.getFieldError('password').length) return
    try {
      await postLoginMutation({ account, password }).unwrap()
      dispatch(updateRememberAccount({ account: remember ? account : undefined }))
    } catch (e: any) {
      setErrorText(e.status >= 401 && e.status <= 403 ? e.data.detail : i18n.t('error_contents.server_error'))
    }
  }

  const handleSubmit = async () => {
    setLoading(true)
    setErrorText('')
    try {
      await form.validateFields()
      await postLogin()
    } finally {
      setLoading(false)
    }
  }

  return (
    <section className="login-page">
      <Content>
        <article>
          <h1 className="system-title">{systemTitle.head ?? i18n.t('titles.system')}</h1>
          <Form
            form={form}
            name="login"
            initialValues={{ account: rememberAccount, remember: rememberAccount !== undefined }}
            autoComplete="off"
            wrapperCol={{ span: 24 }}
            style={{ maxWidth: 600 }}
          >
            <div className="form-input-group">
              <Form.Item<FieldType>
                name="account"
                validateTrigger="onSubmit"
                rules={[requiredRules('account'), minLengthRules('account', 6)]}
                validateStatus={errorText ? 'error' : ''}
              >
                <Input
                  placeholder={`${i18n.t('titles.account')}*`}
                  maxLength={32}
                />
              </Form.Item>
              <Form.Item<FieldType>
                name="password"
                validateTrigger="onSubmit"
                rules={[requiredRules('password'), minLengthRules('password', 6)]}
                validateStatus={errorText ? 'error' : ''}
              >
                <Input.Password
                  placeholder={`${i18n.t('titles.password')}*`}
                  maxLength={16}
                />
              </Form.Item>
            </div>
            <Form.Item style={{ textAlign: 'center' }}>
              {
                errorText && (
                  <Form.Item className="error-prompt" style={{ marginBottom: 16 }}>
                    <div className="error-prompt-content">{errorText}</div>
                  </Form.Item>
                )
              }
              <Flex justify="center" gap={8}>
                <Button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading}
                  className="flex-center"
                >
                  <Spin
                    spinning={loading}
                    delay={300}
                    indicator={(
                      <LoadingIcon2
                        className="spin-animation"
                        style={{ width: 16, height: 16 }}
                      />
                    )}
                  />
                  {i18n.t('buttons.login')}
                </Button>
              </Flex>
            </Form.Item>
            <Form.Item<FieldType>
              name="remember"
              valuePropName="checked"
              style={{ textAlign: 'center', textWrap: 'nowrap' }}
            >
              <Checkbox>{i18n.t('checkboxes.remember_me')}</Checkbox>
            </Form.Item>
          </Form>
        </article>
      </Content>
      {
        window.config.env.UDI && window.config.env.serialNumber && (
          <footer className="login-footer">
            <p className="tag">{i18n.t('paragraphs.udi')}</p>
            <p>*{window.config.env.UDI}*</p>
            <p>{i18n.t('paragraphs.serial_number')}: {window.config.env.serialNumber}</p>
          </footer>
        )
      }
    </section>
  )
}

export default Login
