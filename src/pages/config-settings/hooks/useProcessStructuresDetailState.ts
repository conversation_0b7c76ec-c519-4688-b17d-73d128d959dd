import { useEffect, useState } from 'react'

import { useNavigate, useParams } from 'react-router'

import useAlert from 'src/hooks/useAlert'
import useSortStructure from 'src/hooks/useSortStructure'
import i18n from 'src/i18n'
import {
  useGetProtocolConfigMutation,
  useGetRemoteConfigMutation,
  useGetStructureConfigMutation,
  useGetProcessStructuresDetailMutation,
  usePostProcessStructuresMutation,
  usePutProcessStructuresMutation,
} from 'src/services/api'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { updateConfigRequired } from 'src/store/reducers/configSlice'
import {
  updateInput,
  updateRemote,
  updateDetail,
} from 'src/store/reducers/detailSlice'
import { removeId } from 'src/utils/helper'

const useProcessStructuresDetailState = () => {
  // state
  const [loading, setLoading] = useState(true)
  const [routerBlocker, setRouterBlocker] = useState<boolean>(false)
  const [routerConfig, setRouterConfig] = useState<ToNav>({
    state: false,
    url: '/process-structures',
  })

  // redux
  const dispatch = useAppDispatch()
  const {
    detail: detailState,
    isDetailUpdate,
  } = useAppSelector((state) => state.detailReducer) as {
    detail: ProcessStructuresDetailType,
    isDetailUpdate: boolean
  }
  const { configRequired } = useAppSelector((state) => state.configReducer)
  const { protocolConfig } = useAppSelector((state) => state.configReducer)

  // router
  const processStructuresId = Number(useParams().id)
  const navigate = useNavigate()

  // hook
  const { updateSortAfterSendApi } = useSortStructure()
  const handleAlert = useAlert()

  // api
  const [getProcessStructuresDetailMutation] = useGetProcessStructuresDetailMutation()
  const [getStructureConfigMutation] = useGetStructureConfigMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [getProtocolConfigMutation] = useGetProtocolConfigMutation()
  const [postProcessStructuresMutation] = usePostProcessStructuresMutation()
  const [putProcessStructuresMutation] = usePutProcessStructuresMutation()

  const getApiData = async () => {
    try {
      await getStructureConfigMutation().unwrap()
      await getRemoteConfigMutation().unwrap()
      await getProtocolConfigMutation().unwrap()
      if (processStructuresId) {
        await getProcessStructuresDetailMutation({ id: processStructuresId }).unwrap()
      }
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setLoading(false)
    }
  }

  // handlers
  const handleChangeInput = (label: string, value: string) => {
    dispatch(updateInput({ label, value }))
    if (label in configRequired && value.length > 0) {
      dispatch(updateConfigRequired({ rules: { [label]: false } }))
    }
  }

  const handleChangeUseTemplate = async (value?: string) => {
    const templateId = Number(value)
    if (!templateId) return
    try {
      // Note: This would need to be implemented based on your template API
      // For now, we'll just update the template selection
      dispatch(updateDetail({ use_template: templateId }))
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleUpdateRemote = (type: SourceDestinationKey, remote: RemoteCategoryKey, value: RemoteType[]) => {
    dispatch(updateRemote({ type, remote, value }))
  }

  const handleRemoveDestination = () => {
    dispatch(updateRemote({
      type: 'destination',
      remote: 'remote_server',
      value: [],
    }))
    dispatch(updateRemote({
      type: 'destination',
      remote: 'folder',
      value: [],
    }))
  }

  //  handle ok in save modal
  const updateProcessStructures = async () => {
    setRouterBlocker(false)
    const { updatedStructures, updatedCustomizedStructures } = await updateSortAfterSendApi()
    try {
      if (processStructuresId) {
        await putProcessStructuresMutation({
          id: processStructuresId,
          patient_id: detailState.patient_id,
          rs_set_label: detailState.rs_set_label,
          series_description: detailState.series_description,
          use_template: detailState.use_template,
          template_description: detailState.template_description,
          destination: detailState.destination,
          structures: updatedStructures,
          customized_structures: removeId(updatedCustomizedStructures),
        }).unwrap()
      } else {
        await postProcessStructuresMutation({
          patient_id: detailState.patient_id,
          rs_set_label: detailState.rs_set_label,
          series_description: detailState.series_description,
          use_template: detailState.use_template,
          template_description: detailState.template_description,
          destination: detailState.destination,
          structures: updatedStructures,
          customized_structures: removeId(updatedCustomizedStructures),
        }).unwrap()
      }
      setRouterConfig((prev) => ({ ...prev, state: true }))
    } catch (e) {
      setRouterBlocker(true)
      console.error('(e as Err).data?.detail,', (e as Err).data?.detail)

      handleAlert({
        title: i18n.t('error_titles.save'),
        content: (e as Err).data?.detail,
      }, 'Msg', 'error')
    }
  }

  const handleProcess = () => {
    if (isDetailUpdate) {
      updateProcessStructures()
    } else {
      navigate(routerConfig.url, { replace: true })
    }
  }

  useEffect(() => {
    getApiData()
  }, [])

  useEffect(() => {
    setRouterBlocker(isDetailUpdate)
  }, [isDetailUpdate])

  useEffect(() => {
    if (routerConfig.state) navigate(routerConfig.url, { replace: true })
  }, [routerConfig])

  return {
    detailState,
    loading,
    leaveBlocker: routerBlocker && isDetailUpdate,
    protocolConfig,
    handleChangeUseTemplate,
    handleChangeInput,
    handleUpdateRemote,
    handleRemoveDestination,
    handleProcess,
  }
}

export default useProcessStructuresDetailState
