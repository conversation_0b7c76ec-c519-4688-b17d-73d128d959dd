import { useEffect, useState } from 'react'

import { useLocation, useNavigate, useParams } from 'react-router'

import useAlert from 'src/hooks/useAlert'
import useSortStructure from 'src/hooks/useSortStructure'
import i18n from 'src/i18n'
import {
  useGetProtocolDetailMutation,
  useGetStructureConfigMutation,
  useGetRemoteConfigMutation,
  usePutProtocolMutation,
  usePostProtocolMutation,
} from 'src/services/api'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { resetConfigState, updateConfigRequired } from 'src/store/reducers/configSlice'
import {
  addStudyInfo, deleteStudyInfo, resetDetailState, updateDetail, updateInput, updateRemote, updateStudyInfo,
} from 'src/store/reducers/detailSlice'
import { removeId } from 'src/utils/helper'

type GetProtocolDetail = {
  default: () => Promise<void>
  create: () => Promise<void>
}

const useProtocolDetailState = (type: keyof GetProtocolDetail = 'default') => {
  // state
  const [loading, setLoading] = useState(true)
  const [routerBlocker, setRouterBlocker] = useState<boolean>(false)
  const [isNavigate, setIsNavigate] = useState<boolean>(false)

  // redux
  const dispatch = useAppDispatch()
  const {
    detail: detailState,
    isDetailUpdate,
  } = useAppSelector((state) => state.detailReducer) as { detail: ProtocolDetailType, isDetailUpdate: boolean }
  const { configRequired } = useAppSelector((state) => state.configReducer)
  // router
  const { id: protocolId } = useParams()
  const numberProtocolId = Number(protocolId)
  const navigate = useNavigate()
  const { state } = useLocation()
  // hook
  const { updateSortAfterSendApi } = useSortStructure()
  const handleAlert = useAlert()

  // api
  const [getProtocolDetailMutation] = useGetProtocolDetailMutation()
  const [getStructureConfigMutation] = useGetStructureConfigMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [putProtocolMutation] = usePutProtocolMutation()
  const [postProtocolMutation] = usePostProtocolMutation()

  const getProtocolDetail: GetProtocolDetail = {
    async default() {
      await getProtocolDetailMutation({ id: numberProtocolId }).unwrap()
    },
    async create() {
      if (state) {
        const { protocolName, copyProtocolID } = state
        if (copyProtocolID !== 'create-protocol') {
          await getProtocolDetailMutation({ id: Number(copyProtocolID) }).unwrap()
        }
        dispatch(updateDetail({ protocol_name: protocolName }))
      }
    },
  }

  // fetch
  const getApiData = async () => {
    setLoading(true)
    try {
      await getStructureConfigMutation().unwrap()
      await getRemoteConfigMutation().unwrap()
      await getProtocolDetail[type]()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
    setLoading(false)
  }

  const protocolSaved = async () => {
    setRouterBlocker(false)
    const { updatedStructures, updatedCustomizedStructures } = await updateSortAfterSendApi()
    if (isDetailUpdate) {
      try {
        await postProtocolMutation({
          protocol_name: detailState.protocol_name,
          description: detailState.description,
          structure_set_label: detailState.structure_set_label,
          status: detailState.status,
          source: detailState.source,
          destination: detailState.destination,
          structures: updatedStructures,
          study_info: removeId(detailState.study_info),
          customized_structures: removeId(updatedCustomizedStructures),
        }).unwrap()

        // clear redux state
        dispatch(resetDetailState())
        dispatch(resetConfigState())
        setIsNavigate(true)
      } catch (e) {
        setRouterBlocker(true)
      }
    }
  }

  // handlers
  const studyInfoHandlers = {
    data: detailState.study_info,
    onAdd(data: StudyInformationType) {
      dispatch(addStudyInfo({ data }))
    },
    onDelete(data: StudyInformationType) {
      dispatch(deleteStudyInfo({ data }))
    },
    onChange(id: number, fieldName: string, data: StudyInformationType) {
      dispatch(updateStudyInfo({ data: { id, [fieldName]: data } }))
    },
  }
  const handleChangeInput = (label: string, value: string) => {
    dispatch(updateInput({ label, value }))
    if (label in configRequired && value.length > 0) {
      dispatch(updateConfigRequired({ rules: { [label]: false } }))
    }
  }

  const handleUpdateRemote = (
    key: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[],
  ) => {
    dispatch(updateRemote({ type: key, remote, value }))
  }

  const updateProtocol = async () => {
    setRouterBlocker(false)
    const { updatedStructures, updatedCustomizedStructures } = await updateSortAfterSendApi()
    try {
      await putProtocolMutation({
        id: numberProtocolId,
        protocol_name: detailState.protocol_name,
        description: detailState.description,
        structure_set_label: detailState.structure_set_label,
        status: detailState.status,
        source: detailState.source,
        destination: detailState.destination,
        structures: updatedStructures,
        study_info: removeId(detailState.study_info),
        customized_structures: updatedCustomizedStructures,
      }).unwrap()
      setIsNavigate(true)
    } catch (e) {
      setRouterBlocker(true)
      handleAlert({
        title: i18n.t('error_titles.save'),
        content: (e as Err).data?.detail,
      }, 'Msg', 'error')
    }
  }

  const handleOk = () => {
    if (isDetailUpdate) {
      updateProtocol()
    } else {
      navigate('/protocols', { replace: true })
    }
  }

  useEffect(() => {
    getApiData()
  }, [])

  useEffect(() => {
    setRouterBlocker(isDetailUpdate)
  }, [isDetailUpdate])

  useEffect(() => {
    if (isNavigate) navigate('/protocols', { replace: true })
  }, [isNavigate])

  return {
    detailState,
    loading,
    leaveBlocker: routerBlocker && isDetailUpdate,
    studyInfoHandlers,
    isDetailUpdate,
    handleChangeInput,
    handleUpdateRemote,
    handleOk,
    setRouterBlocker,
    protocolSaved,
  }
}

export default useProtocolDetailState
