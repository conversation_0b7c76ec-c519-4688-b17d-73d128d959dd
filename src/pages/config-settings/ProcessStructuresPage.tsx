import React, { useState } from 'react'

import { type TabsProps, Button } from 'antd'

import SendToDesination from '@/components/Form/SendToDesination'
import ConfigTabItemLabel from '@/components/Tabs/ConfigTabItemLabel'
import TransferTabs from '@/components/Transfer/TransferTabs'
import { ConfigDescriptionItem } from '@/context/moduleList'
import i18n from '@/i18n'
import { BasicLayout, StructuresTabs } from '@/layouts/NewConfigLayout'
import { useAppSelector } from '@/store/hook'

import useProcessStructuresDetailState from './hooks/useProcessStructuresDetailState'

function ProcessStructuresPage() {
  const {
    detailState,
    loading,
    leaveBlocker,
    protocolConfig,
    handleChangeInput,
    handleChangeUseTemplate,
    handleUpdateRemote,
    handleRemoveDestination,
    handleProcess,
  } = useProcessStructuresDetailState()

  const { configRequired } = useAppSelector((state) => state.configReducer)

  // For template selection modal - you may need to implement this based on your requirements
  const [isCorfirmModal] = useState(false)

  // data
  const configHeader: ConfigDescriptionItem[] = [
    { label: 'patient_id', value: detailState.patient_id },
    {
      label: 'rs_set_label',
      tooltip: i18n.t('tooltips.structure_set_label'),
      type: 'input',
      required: true,
      props: {
        value: detailState.rs_set_label,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('rs_set_label', e.target.value)
        },
        status: configRequired.structure_set_label && 'error',
      },
    },
    {
      label: 'use_template',
      type: 'select',
      props: {
        options: protocolConfig.map((item) => ({ value: item.id, label: item.protocol_name })),
        value: detailState.use_template ?? i18n.t('titles.customized'),
        onChange(val: string) {
          if (isCorfirmModal) {
            // You may need to implement confirmation modal logic here
            // Handle template selection confirmation
          } else {
            handleChangeUseTemplate(val)
          }
        },
      },
    },
    { label: 'series_description', value: detailState.series_description },
    {
      label: 'template_description',
      type: 'input',
      props: {
        value: detailState.template_description,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('template_description', e.target.value)
        },
      },
    },
  ]

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <ConfigTabItemLabel warn={configRequired.destination}>
          {i18n.t('titles.destination')}
        </ConfigTabItemLabel>
      ),
      children: (
        <SendToDesination
          defaultOpen={Object.values(detailState.destination).some((list) => list.length > 0)}
          onChange={(checked) => !checked && handleRemoveDestination()}
        >
          <TransferTabs
            type="destination"
            checkList={detailState}
            onUpdateRemote={handleUpdateRemote}
          />
        </SendToDesination>
      ),
    },
    {
      key: '2',
      label: (
        <ConfigTabItemLabel warn={(configRequired.structures || !!configRequired.customized_structures.length)}>
          {i18n.t('titles.structures')}
        </ConfigTabItemLabel>
      ),
      children: (
        <StructuresTabs />
      ),
    },
  ]

  return (
    <BasicLayout
      header={{
        navigatePage: '/rs/queue',
        children: i18n.t('buttons.process_structures'),
      }}
      descriptionData={configHeader}
      loading={loading}
      tabs={tabItems}
      footer={(
        <Button color="primary" variant="outlined" onClick={handleProcess}>
          {i18n.t('buttons.process')}
        </Button>
      )}
      leaveBlocker={leaveBlocker}
    />
  )
}

export default ProcessStructuresPage
