import React from 'react'

import { But<PERSON>, Modal, type TabsProps } from 'antd'
import { useNavigate } from 'react-router'

import { StudyInfoTable } from 'src/components/Table'
import ConfigTabItemLabel from 'src/components/Tabs/ConfigTabItemLabel'
import type { ConfigDescriptionItem } from 'src/context/moduleList'
import { useAntModal } from 'src/hooks/useAntModal'
import useInvalidField from 'src/hooks/useInvalidField'
import i18n from 'src/i18n'
import { RemoteTransferTabs } from 'src/layouts'
import { BasicLayout, StructuresTabs } from 'src/layouts/NewConfigLayout'
import { useAppSelector } from 'src/store/hook'
import { resetAntModalProps, saveAntModalProps } from 'src/utils/saveButton'

import useProtocolDetailState from './hooks/useProtocolDetailState'

function NewProtocolConfigPage() {
  const navigate = useNavigate()

  // redux
  const { configRequired } = useAppSelector((state) => state.configReducer)

  // hook
  const {
    detailState, loading, leaveBlocker,
    studyInfoHandlers, isDetailUpdate,
    handleChangeInput,
    handleUpdateRemote,
    handleOk, setRouterBlocker,
  } = useProtocolDetailState()

  const { verifyDetail } = useInvalidField(detailState)

  // modal
  const saveModal = useAntModal({
    triggerProps: {
      ...saveAntModalProps?.triggerProps,
      onClick: verifyDetail,
    },
    modalProps: {
      onOk: handleOk,
      ...saveAntModalProps?.modalProps,
    },
  })
  const resetModal = useAntModal({
    triggerProps: {
      ...saveAntModalProps?.triggerProps,
      onClick: () => setRouterBlocker(false),
    },
    modalProps: {
      onOk: () => navigate('/protocols', { replace: true }),
      onCancel: () => setRouterBlocker(isDetailUpdate),
      ...resetAntModalProps?.modalProps,
    },
  })

  // data
  const configHeader: ConfigDescriptionItem[] = [
    {
      label: 'protocol_name',
      required: true,
      type: 'input',
      props: {
        value: detailState.protocol_name,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('protocol_name', e.target.value)
        },
        status: configRequired.name && 'error',
      },
    },
    {
      label: 'description',
      type: 'input',
      props: {
        value: detailState.description,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('description', e.target.value)
        },
      },
    },
    {
      label: 'structure_set_label',
      tooltip: i18n.t('tooltips.structure_set_label'),
      type: 'input',
      required: true,
      props: {
        value: detailState.structure_set_label,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('structure_set_label', e.target.value)
        },
        status: configRequired.structure_set_label && 'error',
      },
    },
    {
      label: 'status',
      type: 'switch',
      value: detailState.status,
      props: {
        checked: detailState.status === 'ACTIVE',
        onChange(value: boolean) {
          handleChangeInput('status', value ? 'ACTIVE' : 'INACTIVE')
        },
      },
    },
  ]

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <ConfigTabItemLabel warn={configRequired.source || configRequired.destination}>
          {i18n.t('titles.source_destination')}
        </ConfigTabItemLabel>
      ),
      children: (<RemoteTransferTabs detail={detailState} onUpdateRemote={handleUpdateRemote} />),
    },
    {
      key: '2',
      label: (
        <ConfigTabItemLabel warn={(configRequired.study_info)}>
          {i18n.t('titles.study_info')}
        </ConfigTabItemLabel>
      ),
      children: (<StudyInfoTable {...studyInfoHandlers} />),
    },
    {
      key: '3',
      label: (
        <ConfigTabItemLabel warn={(configRequired.structures || !!configRequired.customized_structures.length)}>
          {i18n.t('titles.structures')}
        </ConfigTabItemLabel>
      ),
      children: (<StructuresTabs />),
    },
  ]

  return (
    <>
      <BasicLayout
        header={{
          navigatePage: '/protocols',
          children: i18n.t('titles.config_setting'),
        }}
        descriptionData={configHeader}
        descriptionsProps={{
          column: {
            md: 2, lg: 2, xl: 2, xxl: 2,
          },
        }}
        loading={loading}
        tabs={tabItems}
        footer={(
          <>
            <Button {...resetModal.triggerProps}>
              {i18n.t('buttons.reset')}
            </Button>
            <Button {...saveModal.triggerProps} />
          </>
        )}
        leaveBlocker={leaveBlocker}
      />
      {/* save & draw moda; */}
      <Modal {...resetModal.modalProps} styles={{ body: { fontSize: '18px', padding: '4.rem 2rem' } }} />
      <Modal {...saveModal.modalProps} />
    </>
  )
}

export default NewProtocolConfigPage
