type ManualJSONData = {
  tag?: keyof JSX.IntrinsicElements
  content: string
  figcaption?: string
}

type ItemElement = {
  tag: JSX.ElementType,
  children: string
  figcaption?: string
}

type ListElement = {
  tag: 'ul',
  children: ItemElement[]
}

type BaseElement = ListElement | ItemElement

type FaqJSONData = {
  question: string
  answer: string
}

type AboutList = {
  Title?: string
  type: 'image' | 'head' | 'text' | 'list'
  Row: string
  Description?: string
}

interface AboutDataSource {
  container: AboutList[],
}
