interface ImageRSList {
  id: number
  rs_set_label: string
  series_number: number
  rs_set_date: string
  import_time: string
}

interface RsQueueGroupType {
  patient_id: string
  patient_name: string
  study_date: string
  study_uid: string
}

interface RsTemplateItemType {
  id: number
  name: string
  description?: string
  last_modified: string
}

interface RsTemplateItemDetailType {
  name: string
  description?: string
  rs_set_label: string
  send_to_destination: boolean,
  destination: RemoteCategoryType
  customized_structures: CustomizedStructuresType[]
}
