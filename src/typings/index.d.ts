export { }

declare global {
  interface Window {
    config: {
      env: {
        systemTitle: {
          head: string,
          autoSeg: string,
        },
        api: {
          rest: string,
          websocket: string,
        };
        UDI: string
        serialNumber: string
        manual: {
          isShow: boolean
          fileName: string
          sheet: {
            direction: string
            FAQ: string
          }
        }
        configPage: {
          marginValueLimit: number
        }
        about: {
          fileName: string;
          sheet: {
            container: string;
          },
        }
        demo: boolean
      };
    };
  }
}
