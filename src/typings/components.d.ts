type ModalBtnType = {
  btnName?: string | JSX.Element
  btnType?: 'button' | 'submit' | 'reset'
  btnClass?: string
  btnStyle?: React.CSSProperties
  btnClick?: (() => void)
  btnDisabled?: boolean
  modalTitle?: string
  modalContent?: string | JSX.Element
  modalFooter?: JSX.Element | React.ReactNode | null
  okText?: string
  onOk?: (() => void) | (() => Promise<void>);
  cancelText?: string
  onCancel?: () => void
  modalClassName?: string
  modalStyle?: React.CSSProperties
  modalBodyStyle?: React.CSSProperties
  width?: number
  tooltip?: string
}

type FooterBtnType = ModalBtnType & {
  type?: 'modal-button' | 'button'
}

type SeriesNavbar = {
  study_description: string
  last_modified: string
  series_count: number
}
