type Err = {
  status: number
  data: {
    detail?: string
  }
}

type SourceDestinationKey = keyof SourceDestinationType

type SourceDestinationType = {
  source: RemoteCategoryType
  destination: RemoteCategoryType
}

type WorklistInfo = {
  patient_id: string
  structure_set_label: string
  use_protocol: int
  study_date: string
  study_description?: string
  protocol_description?: string
  series_time: string
  series_description?: string
}

type WorklistDetailType = WorklistInfo & Pick<SourceDestinationType, 'destination'> & {
  structures: StructureSortType[]
  customized_structures: CustomizedStructuresType[]
}

type WorklistProtocolType = {
  protocol_id: number
}

type CustomizedWorklistDetailType = {
  structure_set_label: string
  description?: string
  destination: RemoteCategoryType
  structures: StructureSortType[]
  customized_structures: CustomizedStructuresType[]
}

type OptionType = {
  value: string
  label: string
}

type NotifyType = {
  id: number
  type: 'inference successed' | 'inference failed' | 'system disconnected' | 'warning'
  patient_id: string
  message: string
  time: string
}

type WorklistGroupType = {
  id: number
  insert: boolean
  patient_id: string
  study_date: string
  study_status: string
  progress: number
}

type WorklistType = {
  series_count: number
  study_description: string
  last_modified: string
  series: SeriesType[]
}

type ModalityType = 'CT' | 'RS' | 'MR'

// New API response types based on backend confirmation
type HistoryWorklistItemType = {
  id: number
  image_count: number
  status: number
  protocol: number
}

type HistorySeriesType = {
  viewer_series_id: string
  series_number: number
  series_description: string
  instance_count: number
  worklist: HistoryWorklistItemType[]
}

type HistoryStudyType = {
  viewer_study_id: string
  study_date: string
  study_description: string
  total_modality: ModalityType[]
  series: HistorySeriesType[]
}

type HistoryWorklistResponseType = {
  study: HistoryStudyType[]
}

// Legacy types for backward compatibility
type HistoryGroupType = {
  group_id: number
  modality: ModalityType
  images: number
  re_set_label?: string
  series_number: number
  series_description?: string
  source?: string
  status?: string
}

type HistoryWorklistType = {
  list_id: number
  study_date: string
  study_description: string
  modality: ModalityType[]
  group: Array<HistoryGroupType & { items: HistoryGroupType[] }>
}

type PreviewSeriesInfoType = {
  patientID: string | null
  studyDate: string
  series: number
  seriesDescription: string
}

type WorklistGroupPageType = {
  current: number
  total: number
  size: number
}

type WorklistGroupFocusType = {
  id?: number
  timeStamp?: number
}

type WorklistGroupSearchType = {
  patient_id: string | undefined
  study_status: string | undefined
  study_date_range_start: string | undefined
  study_date_range_end: string | undefined
  order_key: string
  ascend: boolean
}

type SeriesType = {
  worklist_id: number
  number: number
  time: string
  last_modified: string
  protocol_name: string
  image: number
  status: string
  description: string
}

type StructureType = {
  id: number
  efai_structure_name: string
  customized_name?: string
  definition?: string
  color_code: string
}

type StructureConfigType = {
  id: number
  efai_structure_name: string
  customized_name?: string
  color_code: string
  category: string[]
  tag: string[]
}

type ProtocolType = {
  id: number
  priority: number
  protocol_name: string
  last_mod_time: string
  description?: string
  status: string
}

type ProtocolConfigType = {
  id: number
  protocol_name: string
}

type ProtocolInfo = {
  protocol_name: string
  description: string
  structure_set_label: string
  status: string
}

type StructureSortType = {
  id: number
  sort: number | null
}

type CommonInfo = WorklistInfo | ProtocolInfo

type ProtocolDetailType = ProtocolInfo & SourceDestinationType & {
  structures: StructureSortType[]
  study_info: StudyInformationType[]
  customized_structures: CustomizedStructuresType[]
}

type ProcessStructuresInfo = {
  patient_id: string
  rs_set_label: string
  series_description: string
  use_template: number | null
  template_description: string
}

type ProcessStructuresDetailType = ProcessStructuresInfo & Pick<SourceDestinationType, 'destination'> & {
  structures: StructureSortType[]
  customized_structures: CustomizedStructuresType[]
}

type RemoteCategoryKey = keyof RemoteCategoryType

type RemoteType = {
  id: number
  rs_transfer?: boolean
  ct_transfer?: boolean
}

type RemoteCategoryType = {
  remote_server: RemoteType[]
  folder: RemoteType[]
}

type RemoteServerType = {
  id: number
  connect_status: string
  name: string
  description: string
  ae_title: string
  ip: string
  port: string
  type: string
}

type RemoteConfigType = {
  id: number
  name: string
  type: string
}

type FolderType = RemoteConfigType & {
  description: string
  path: string
  connect_status: string
}

type StudyInformationType = {
  id?: number
  dicom_tag: string
  format: string
  value: string
}

type OperationsBasicMode = 'EMPTY_STRUCTURE' | 'COMBINE' | 'CROP' | 'OVERLAP' | ''
type OperationsBasic = {
  mode: OperationsBasicMode
  margin_value: null
}

type OperationsMarginMode = 'OUTER' | 'INNER'
type OperationsMargin = {
  mode: OperationsMarginMode
  margin_value: {
    anterior: number | null
    posterior: number | null
    left: number | null
    right: number | null
    superior: number | null
    inferior: number | null
  }
}
type MarginValueKey = keyof OperationsMargin['margin_value']

type CustomizedStructuresType = (OperationsBasic | OperationsMargin) & {
  id: number
  show: boolean
  name?: string
  color_code: string
  type?: string
  sort: number | null
  structure_operations?: number[]
  margin_symmetric: boolean
}

type SortableDataType = {
  id: number
  name: string | undefined
  mode?: OperationsBasicMode | OperationsMarginMode | undefined
  color_code: string
  sort: number
}

type CustomizedStructuresChangeDataType = number | string | string

type AddRemoteServerType = {
  name: string
  ae_title: string
  description: string
  ip: string
  port: string
}

type AddFolderType = {
  name: string
  path: string
  description: string
  account?: string
  password?: string
}

type ToNav = {
  state: boolean
  url: string
}

/* OperationBtnGroup */

type OperationBtnGroup = {
  isDisabled: boolean
}

type TransferTag = {
  key: string
  title: string
}

type InputMap = {
  [key: string]: string
}

type ListLayoutType = RemoteServerType | FolderType | StudyInformationType | CustomizedStructuresType

type IDOrTextOrUndefined = number | string | undefined
