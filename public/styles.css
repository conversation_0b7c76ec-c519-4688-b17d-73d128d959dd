:root {
  --color-primary_lightest: #54ccd1;
  --color-primary_light: #1fb5b5;
  --color-primary: #00b3b9;
  --color-primary_variants: #0a888c;
  --color-success: #8cc88e;
  --color-error: #e53d00;
  --color-gray_0: #ffffff;
  --color-gray_0_alpha: rgba(255, 255, 255, 0.15);
  --color-gray_0_variants: rgba(255, 255, 255, 0.8);
  --color-gray_1: #c0c0c0;
  --color-gray_1_variants: #576067;
  --color-gray_2: #434a4f;
  --color-gray_3: #363d42;
  --color-gray_4: #2e3438;
  --color-gray_5: #282e32;
  --color-gray_6: #1a262f;
  --ffffff-15: rgba(255, 255, 255, 0.15);
  --scroll-bar-color: var(--color-gray_6);
  --manual-border-color: rgba(255, 255, 255, 0.15);
  --shine: rgba(255, 255, 255, 0.3);
}

.overflow-hidden {
  overflow: hidden;
}

#root-rending .root-spin-animation {
  display: none;
}
#root-rending {
  position: fixed;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--color-gray_5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 確保在最上層 */
}

.fade-out {
  opacity: 1; /* 起始透明度 */
  animation: fadeOut 0.8s forwards;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes logo-ease-in-out {
  0%,
  100% {
    opacity: 0.1;
  }
  50% {
    opacity: 1;
  }
}

#root-rending.fade-in-out .logo-img {
  opacity: 0.1;
  animation: logo-ease-in-out 5s infinite ease-in-out;
}

.img-container {
  position: relative;
  width: 300px;
  height: 100px;
}

.logo-img {
  display: block;
  width: 100%;
  height: auto;
}

.shine .shine-overlay {
  position: absolute;
  top: 0;
  pointer-events: none;
  mask-image: url("./images/logo.svg");
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-image: url("./images/logo.svg");
  -webkit-mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  width: 300px;
  height: 112px;
  pointer-events: none;
  /* animation: shine 2s linear infinite; */
}

.shine .shine-overlay::after {
  content: "";
  position: absolute;
  top: 0;
  left: -75%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 35%, rgba(255, 255, 255, 0.75) 50%, transparent 65%);
  background-size: 120%;
  animation: shine 2s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shine {
  0% {
    transform: translateX(-50%);
  }
  56.5%,
  100% {
    transform: translateX(200%);
  }
}
