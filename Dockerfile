# build environment
FROM node:16.17.0 as build
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
COPY package.json /app/package.json
RUN npm install 
COPY . /app
RUN SKIP_PREFLIGHT_CHECK=true CI= npm run build

# production environment
FROM nginx:1.19.5-alpine
ARG WTH
ENV WTH=$WTH
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 80
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf
CMD ["nginx", "-g", "daemon off;"]
