{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "esModuleInterop": true, "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "types": ["vite-plugin-svgr/client", "vitest/globals", "@testing-library/jest-dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}