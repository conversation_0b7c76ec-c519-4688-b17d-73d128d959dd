# RT-HCAPSeg-web

## 📋 專案概述

這是 EFAI AutoSeg V3 系統的 Web 前端介面，基於 React 進行開發，提供完整的工作清單管理、協議配置、歷史記錄查詢等功能。

## 🎯 主要功能

- **任務列表管理**: 支援三層嵌套表格結構 (Study/Series/Worklist)，提供批次選擇和操作功能
- **歷史記錄查詢**: 完整的歷史工作清單查看和管理
- **協議管理**: 協議配置、建立、編輯和刪除功能，支援新舊兩套配置系統
- **結構管理**: 醫療影像結構配置和編輯
- **遠端管理**: 來源和目標伺服器連線配置
- **使用手冊**: 內建 FAQ 和操作說明，支援 Excel 檔案解析顯示
- **即時通訊**: WebSocket 整合，提供即時工作清單更新和通知

## 🛠️ 技術架構

### 核心技術棧

- **前端框架**: React 18.2 + TypeScript
- **建置工具**: Vite 4.4
- **UI 框架**: Ant Design 5.x
- **路由**: React Router 7.x
- **狀態管理**: Redux Toolkit + RTK Query
- **樣式**: CSS + Ant Design 主題客製化
- **國際化**: i18next
- **測試**: Vitest + React Testing Library + MSW
- **程式碼品質**: ESLint (Airbnb 規範)

## 📋 系統需求

- **Node.js**: 16+ (Vite 4.4 需要 Node.js 16+)
- **瀏覽器**: Chrome 88+ (支援現代 CSS 特性)

### 瀏覽器相容性

| 特性                                                                                    | antd 版本 | 相容性                                           | 最低Chrome 版本 | 降級相容方案                                                          |
| --------------------------------------------------------------------------------------- | --------- | ------------------------------------------------ | --------------- | --------------------------------------------------------------------- |
| [:where 選擇器](https://developer.mozilla.org/en-US/docs/Web/CSS/:where)                | `>=5.0.0` | [caniuse](https://caniuse.com/?search=%3Awhere)  | Chrome 88       | `<StyleProvider hashPriority="high">`                                 |
| [CSS 邏輯屬性](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties) | `>=5.0.0` | [caniuse](https://caniuse.com/css-logical-props) | Chrome 89       | `<StyleProvider transformers={[legacyLogicalPropertiesTransformer]}>` |

## 🚀 快速開始

### 安裝依賴

```bash
npm install
```

### 開發模式

```bash
npm run dev
```

開啟瀏覽器

### 建置專案

```bash
npm run build
```

### 預覽建置結果

```bash
npm run preview
```

## 📜 可用指令

| 指令                    | 說明               |
| ----------------------- | ------------------ |
| `npm install`           | 安裝所有依賴套件   |
| `npm run dev`           | 啟動開發伺服器     |
| `npm run build`         | 建置生產版本       |
| `npm run preview`       | 預覽建置結果       |
| `npm run build:preview` | 建置並預覽         |
| `npm run test`          | 執行測試           |
| `npm run test:ui`       | 執行測試 UI 介面   |
| `npm run coverage`      | 產生測試覆蓋率報告 |
| `npm run lint`          | 執行程式碼檢查     |

## 🏗️ 專案結構

```
src/
├── components/          # 共享 React 元件
├── hooks/               # 自訂 React Hooks
├── layouts/             # 版面配置元件
├── pages/               # 頁面元件
├── services/            # API 服務
│   └── api/            # RTK Query API 定義
├── store/               # Redux 狀態管理
│   └── reducers/       # Redux Reducers
├── styles/              # 樣式檔案
├── tests/               # 測試檔案
├── typings/             # TypeScript 型別定義
└── utils/               # 工具函數
```

## 🔧 核心功能模組

### 狀態管理 (Redux Store)

- `authReducer`: 使用者認證狀態
- `worklistReducer`: 工作清單狀態
- `historyReducer`: 歷史記錄狀態
- `protocolReducer`: 協議管理狀態
- `websocketReducer`: WebSocket 訊息處理
- `configReducer`: 系統配置
- `notifyReducer`: 通知系統

### API 服務模組

- `auth`: 登入/登出認證
- `worklist`: 工作清單 CRUD 操作
- `history`: 歷史記錄查詢
- `protocol`: 協議管理
- `structure`: 結構管理
- `remote`: 遠端伺服器管理
- `download`: 檔案下載
- `notify`: 通知系統

## 🧪 測試

### 執行測試

```bash
# 執行所有測試
npm run test

# 執行測試 UI 介面
npm run test:ui

# 產生覆蓋率報告
npm run coverage
```

### 測試架構

- **單元測試**: Vitest
- **元件測試**: React Testing Library
- **API 模擬**: Mock Service Worker (MSW)
- **測試環境**: jsdom

## 🐳 Docker 部署

### 建置 Docker 映像

```bash
docker build -t rt-hcapseg-web .
```

### 執行容器

```bash
docker run -p 80:80 rt-hcapseg-web
```

## 🔒 安全性

- 實作 JWT 認證機制
- 設定安全性標頭 (X-Frame-Options, CSP)
- 支援 HTTPS 部署
- 輸入驗證和清理

## 🌍 國際化

專案支援多語言，使用 i18next 進行國際化處理。語言檔案位於 `src/assets/locales/` 目錄。

## 📝 開發規範

### 程式碼風格

- 遵循 Airbnb JavaScript/TypeScript 風格指南
- 使用 ESLint 進行程式碼檢查
- TypeScript 嚴格模式啟用

### 命名規範

- 元件檔案: PascalCase (例: `UserCard.tsx`)
- Hook 檔案: useCamelCase (例: `useFetch.ts`)
- 一般檔案/目錄: kebab-case
- 變數/函數: camelCase
- 常數: UPPER_SNAKE_CASE
