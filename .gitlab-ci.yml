stages:
  # - test
  - build
  - deploy
  - deploy-staging

# test:
#   stage: test
#   tags: ["efai_k8s_rd"]
#   image:
#     name: node:16.17.0
#     entrypoint: [""]
#   before_script:
#     - npm install
#   script:
#     - npm run test
#   rules:
#     - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"

# Build and deploy
build:
    stage: build
    tags: ["efai_k8s_rd"]
    image:
      name: gcr.io/kaniko-project/executor:debug
      entrypoint: [""]
    script:
      - if [ "$CI_COMMIT_BRANCH" == "develop" ]; then SUFFIX="-dev"; else SUFFIX=""; fi
      - /kaniko/executor --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/Dockerfile --build-arg WTH=$CI_COMMIT_SHORT_SHA --destination registry.efai.tw/rt/web${SUFFIX}:$CI_COMMIT_SHORT_SHA
    rules:
      - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"

tag-web-latest:
  stage: deploy
  tags: ["efai_k8s_rd"]
  image:
    name: gcr.io/go-containerregistry/crane:debug
    entrypoint: [""]
  variables:
    GIT_STRATEGY: none
  script:
    - if [ "$CI_COMMIT_BRANCH" == "develop" ]; then SUFFIX="-dev"; else SUFFIX=""; fi
    - crane cp registry.efai.tw/rt/web${SUFFIX}:$CI_COMMIT_SHORT_SHA registry.efai.tw/rt/web${SUFFIX}:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"

deploy-web:
  stage: deploy
  tags: ["efai_k8s_rd"]
  variables:
    GIT_STRATEGY: none
  image: bitnami/kubectl:latest
  script:
    - |
      if [ "$CI_COMMIT_BRANCH" == "develop" ]; then SUFFIX="-dev"; else SUFFIX=""; fi
      kubectl -n efai-rt${SUFFIX} set image deployment.apps/web web=registry.efai.tw/rt/web${SUFFIX}:$CI_COMMIT_SHORT_SHA
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"

deploy-staging:
  when: manual
  stage: deploy-staging
  tags: ["efai_k8s_rd"]
  variables:
    GIT_STRATEGY: none
  image: bitnami/kubectl:latest
  script:
    - |
      if [ "$CI_COMMIT_BRANCH" == "develop" ]; then SUFFIX="-dev"; else SUFFIX=""; fi
      kubectl -n efai-rt-staging set image deployment.apps/web web=registry.efai.tw/rt/web${SUFFIX}:$CI_COMMIT_SHORT_SHA
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"


